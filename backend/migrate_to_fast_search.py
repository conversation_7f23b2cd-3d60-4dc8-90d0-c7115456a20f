#!/usr/bin/env python3.11
"""
Migration script to populate Fast Hybrid Search with existing frame data
"""

import sys
import asyncio
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database import get_db
from app.models import VideoFrame
from app.services.optimized_vector_service import OptimizedVectorService

async def migrate_existing_frames():
    """Migrate existing frame descriptions to Fast Hybrid Search index"""
    print('🔄 Migrating existing frames to Fast Hybrid Search...')
    
    try:
        # Initialize services
        optimized_service = OptimizedVectorService()
        if not optimized_service.available:
            print('❌ Optimized Vector Service not available')
            return False
        
        # Get database session
        db = next(get_db())
        
        # Get all frames with descriptions
        frames = db.query(VideoFrame).filter(VideoFrame.description.isnot(None)).all()
        print(f'📊 Found {len(frames)} frames with descriptions')
        
        if not frames:
            print('⚠️ No frames to migrate')
            return True
        
        # Prepare batch data
        frames_data = []
        for frame in frames:
            # Clean up description (remove JSON formatting if present)
            description = frame.description
            if description.startswith('```json'):
                # Extract content between ```json and ```
                start = description.find('{')
                end = description.rfind('}')
                if start != -1 and end != -1:
                    import json
                    try:
                        json_data = json.loads(description[start:end+1])
                        # Extract meaningful description from JSON
                        if 'Objects' in json_data:
                            objects = json_data['Objects']
                            if isinstance(objects, list):
                                description = ', '.join(objects[:5])  # Top 5 objects
                            else:
                                description = str(objects)
                        elif 'description' in json_data:
                            description = json_data['description']
                    except:
                        # If JSON parsing fails, use original
                        pass
            
            frames_data.append({
                'frame_id': f'frame_{frame.id}',
                'description': description,
                'timestamp': frame.timestamp,
                'frame_path': frame.frame_path,
                'video_id': frame.video_id,
                'metadata': {
                    'frame_id': frame.id,
                    'video_id': frame.video_id,
                    'timestamp': frame.timestamp,
                    'frame_path': frame.frame_path
                }
            })
        
        # Batch add frames
        print(f'🚀 Adding {len(frames_data)} frames to Fast Hybrid Search...')
        success_count = await optimized_service.batch_add_frames(frames_data)
        print(f'✅ Successfully migrated {success_count}/{len(frames_data)} frames')
        
        # Get stats
        stats = optimized_service.get_stats()
        print(f'\n📈 Fast Hybrid Search Index Stats:')
        print(f'  ✅ Available: {stats["available"]}')
        print(f'  📊 Total frames: {stats["total_frames"]}')
        print(f'  🔍 FAISS index size: {stats["index_sizes"]["faiss_total"]}')
        print(f'  📝 BM25 documents: {stats["index_sizes"]["bm25_docs"]}')
        print(f'  ⚡ Avg search time: {stats["avg_search_time_ms"]:.2f}ms')
        
        capabilities = stats.get("capabilities", {})
        print(f'\n🔧 Capabilities:')
        for key, value in capabilities.items():
            status = '✅' if value else '❌'
            print(f'  {status} {key}: {value}')
        
        db.close()
        return True
        
    except Exception as e:
        print(f'❌ Migration failed: {e}')
        import traceback
        traceback.print_exc()
        return False

async def test_search():
    """Test the migrated search functionality"""
    print('\n🧪 Testing Fast Hybrid Search...')
    
    try:
        optimized_service = OptimizedVectorService()
        if not optimized_service.available:
            print('❌ Service not available for testing')
            return
        
        # Test queries
        test_queries = ['stage', 'presentation', 'screen', 'speaker', 'Google']
        
        for query in test_queries:
            results = await optimized_service.search_visual(
                query=query,
                video_id=None,  # Search all videos
                limit=3
            )
            print(f'🔍 "{query}": {len(results)} results')
            for i, result in enumerate(results[:2]):
                print(f'  {i+1}. {result.timestamp}s - {result.description[:60]}...')
        
    except Exception as e:
        print(f'❌ Test failed: {e}')

if __name__ == '__main__':
    print('🚀 Fast Hybrid Search Migration Tool')
    print('=' * 50)
    
    # Run migration
    success = asyncio.run(migrate_existing_frames())
    
    if success:
        # Run tests
        asyncio.run(test_search())
        print('\n🎉 Migration completed successfully!')
        print('Your visual search now uses Fast Hybrid Search (BM25 + Dense + FAISS)')
    else:
        print('\n❌ Migration failed!')
        sys.exit(1)
