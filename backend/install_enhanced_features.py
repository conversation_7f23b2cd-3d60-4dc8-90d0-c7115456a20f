#!/usr/bin/env python3
"""
Enhanced Features Installation Script
Installs PyTorch, CLIP, and other multimodal dependencies for Python 3.13
"""

import subprocess
import sys
import platform
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_pip_command():
    """Get the correct pip command for this system"""
    # Try different pip commands
    for cmd in ['pip3', 'pip', 'python3 -m pip', 'python -m pip']:
        try:
            result = subprocess.run(f"{cmd} --version", shell=True, capture_output=True)
            if result.returncode == 0:
                return cmd
        except:
            continue
    return None

def run_command(command, description):
    """Run a command and handle errors"""
    logger.info(f"Installing {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install {description}: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 9:
        logger.error("Python 3.9+ is required for enhanced features")
        return False
    
    if version.minor >= 13:
        logger.info("Python 3.13+ detected - using latest compatible versions")
    
    return True

def install_pytorch():
    """Install PyTorch with CUDA support if available"""
    pip_cmd = get_pip_command()
    if not pip_cmd:
        logger.error("No pip command found. Please install pip first.")
        return False

    system = platform.system().lower()

    # Check for CUDA availability
    try:
        result = subprocess.run("nvidia-smi", shell=True, capture_output=True)
        has_cuda = result.returncode == 0
    except:
        has_cuda = False

    if has_cuda:
        logger.info("CUDA detected, installing PyTorch with CUDA support")
        if system == "windows":
            command = f"{pip_cmd} install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121"
        else:
            command = f"{pip_cmd} install torch torchvision torchaudio"
    else:
        logger.info("No CUDA detected, installing CPU-only PyTorch")
        if system == "windows":
            command = f"{pip_cmd} install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
        else:
            command = f"{pip_cmd} install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"

    return run_command(command, "PyTorch")

def install_enhanced_dependencies():
    """Install all enhanced dependencies"""
    pip_cmd = get_pip_command()
    if not pip_cmd:
        logger.error("No pip command found. Please install pip first.")
        return False

    dependencies = [
        ("sentence-transformers>=2.2.0", "Sentence Transformers"),
        ("transformers>=4.35.0", "Hugging Face Transformers"),
        ("open-clip-torch>=2.24.0", "OpenCLIP"),
        ("accelerate>=0.24.0", "Accelerate"),
        ("huggingface-hub>=0.19.0", "Hugging Face Hub"),
    ]

    success_count = 0
    for package, description in dependencies:
        if run_command(f"{pip_cmd} install {package}", description):
            success_count += 1

    return success_count == len(dependencies)

def verify_installation():
    """Verify that all packages are installed correctly"""
    logger.info("Verifying installation...")
    
    packages_to_check = [
        ("torch", "PyTorch"),
        ("torchvision", "TorchVision"),
        ("sentence_transformers", "Sentence Transformers"),
        ("transformers", "Transformers"),
        ("open_clip", "OpenCLIP"),
    ]
    
    failed_packages = []
    
    for package, name in packages_to_check:
        try:
            __import__(package)
            logger.info(f"✅ {name} imported successfully")
        except ImportError as e:
            logger.error(f"❌ Failed to import {name}: {e}")
            failed_packages.append(name)
    
    if failed_packages:
        logger.error(f"Failed to verify: {', '.join(failed_packages)}")
        return False
    
    # Test PyTorch functionality
    try:
        import torch
        x = torch.randn(2, 3)
        logger.info(f"✅ PyTorch test successful (device: {torch.device('cuda' if torch.cuda.is_available() else 'cpu')})")
    except Exception as e:
        logger.error(f"❌ PyTorch test failed: {e}")
        return False
    
    # Test CLIP functionality
    try:
        import open_clip
        model, _, preprocess = open_clip.create_model_and_transforms('ViT-B-32', pretrained='openai')
        logger.info("✅ OpenCLIP test successful")
    except Exception as e:
        logger.warning(f"⚠️ OpenCLIP test failed: {e}")
        # Try original CLIP as fallback
        try:
            import clip
            model, preprocess = clip.load("ViT-B/32")
            logger.info("✅ Original CLIP test successful")
        except Exception as e2:
            logger.error(f"❌ Both CLIP implementations failed: {e2}")
            return False
    
    return True

def main():
    """Main installation process"""
    logger.info("🚀 Starting Enhanced Features Installation")
    logger.info("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install PyTorch first
    if not install_pytorch():
        logger.error("Failed to install PyTorch. Cannot continue.")
        sys.exit(1)
    
    # Install other dependencies
    if not install_enhanced_dependencies():
        logger.error("Some dependencies failed to install. Check logs above.")
        sys.exit(1)
    
    # Verify installation
    if not verify_installation():
        logger.error("Installation verification failed. Some features may not work.")
        sys.exit(1)
    
    logger.info("=" * 50)
    logger.info("🎉 Enhanced Features Installation Complete!")
    logger.info("Your visual search system now has full multimodal capabilities:")
    logger.info("  ✅ PyTorch for deep learning")
    logger.info("  ✅ CLIP for image-text understanding")
    logger.info("  ✅ Sentence Transformers for text embeddings")
    logger.info("  ✅ Transformers for additional models")
    logger.info("")
    logger.info("Restart your application to use the enhanced features.")

if __name__ == "__main__":
    main()
