{"query": "green", "video_id": 8, "method": "visual_search", "result": {"query": "green", "results": [{"timestamp": 0.0, "confidence": 70.0, "description": "Match for 'green': *   Large medium-green diagonal stripe (top right, adjacent to yellow)...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_0_0.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 85.0, "confidence": 70.0, "description": "Match for 'green': Objects:\n-   Large digital projection screen (dominant feature on the right side, serving as a backdrop)\n-   Stage platform (where the speaker is standing)\n-   Multiple tall, rectangular backlit panel...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_1_85.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 165.0, "confidence": 70.0, "description": "Match for 'green': - Abstract graphic elements: A large diagonal golden yellow band at the top right, a large diagonal vibrant lime green band below it on the right, and a large curved gradient blue to teal shape at the...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_2_165.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 245.0, "confidence": 70.0, "description": "Match for 'green': - Abstract graphical elements: A large, vibrant golden yellow transitioning to lime green diagonal shape in the top right corner...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_3_245.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 305.0, "confidence": 70.0, "description": "Match for 'green': - Abstract graphic element in the top-right corner, primarily yellow/gold, with a smaller green/lime segment below it...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_4_305.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 375.0, "confidence": 70.0, "description": "Match for 'green': ```json\n{\n  \"Objects\": [\n    \"Large presentation screen (dominant right side)\",\n    \"Male presenter/speaker on stage\",\n    \"Stage lighting panels (vertical, illuminated)\",\n    \"Abstract background sha...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_5_375.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 435.0, "confidence": 70.0, "description": "Match for 'green': Objects:\n- Large LED display screen (center-right)\n- Stage platform (below the screen)\n- Abstract graphic elements (golden-yellow and green in top-right, blue-teal gradient in bottom-right)\n- White ba...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_6_435.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 505.0, "confidence": 70.0, "description": "Match for 'green': Objects: [\n  \"Large white background panel (left)\",\n  \"Large black rectangular presentation screen (right)\",\n  \"Elevated stage platform\",\n  \"Three vertical rectangular illuminated panels (stage set de...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_7_505.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 560.0, "confidence": 70.0, "description": "Match for 'green': ]\nColors: [Dominant bright white background (left side), Gradient blue-to-green (for the \"Recap\" text), Dark gray/black (for most text like \"Google Cloud Next '25 Opening Keynote in 10 minutes\" and \"G...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_8_560.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "clips": [{"start_time": 0.0, "end_time": 0.0, "confidence": 70.0, "description": "Clip from 0.0s to 0.0s: Match for 'green': *   Large medium-green diagonal stripe (top right, adjacent to yellow)...", "frame_count": 1, "frames": [{"timestamp": 0.0, "confidence": 70.0, "description": "Match for 'green': *   Large medium-green diagonal stripe (top right, adjacent to yellow)...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_0_0.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 85.0, "end_time": 85.0, "confidence": 70.0, "description": "Clip from 85.0s to 85.0s: Match for 'green': Objects:\n-   Large digital projection screen (dominant feature on the right side, serving as a backdrop)\n-   Stage platform (where the speaker is standing)\n-   Multiple tall, rectangular backlit panel...", "frame_count": 1, "frames": [{"timestamp": 85.0, "confidence": 70.0, "description": "Match for 'green': Objects:\n-   Large digital projection screen (dominant feature on the right side, serving as a backdrop)\n-   Stage platform (where the speaker is standing)\n-   Multiple tall, rectangular backlit panel...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_1_85.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 165.0, "end_time": 165.0, "confidence": 70.0, "description": "Clip from 165.0s to 165.0s: Match for 'green': - Abstract graphic elements: A large diagonal golden yellow band at the top right, a large diagonal vibrant lime green band below it on the right, and a large curved gradient blue to teal shape at the...", "frame_count": 1, "frames": [{"timestamp": 165.0, "confidence": 70.0, "description": "Match for 'green': - Abstract graphic elements: A large diagonal golden yellow band at the top right, a large diagonal vibrant lime green band below it on the right, and a large curved gradient blue to teal shape at the...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_2_165.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 245.0, "end_time": 245.0, "confidence": 70.0, "description": "Clip from 245.0s to 245.0s: Match for 'green': - Abstract graphical elements: A large, vibrant golden yellow transitioning to lime green diagonal shape in the top right corner...", "frame_count": 1, "frames": [{"timestamp": 245.0, "confidence": 70.0, "description": "Match for 'green': - Abstract graphical elements: A large, vibrant golden yellow transitioning to lime green diagonal shape in the top right corner...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_3_245.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 305.0, "end_time": 305.0, "confidence": 70.0, "description": "Clip from 305.0s to 305.0s: Match for 'green': - Abstract graphic element in the top-right corner, primarily yellow/gold, with a smaller green/lime segment below it...", "frame_count": 1, "frames": [{"timestamp": 305.0, "confidence": 70.0, "description": "Match for 'green': - Abstract graphic element in the top-right corner, primarily yellow/gold, with a smaller green/lime segment below it...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_4_305.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}], "total_results": 9, "direct_answer": "Found 9 instances of 'green' in the video", "query_type": null, "summary": null, "processing_method": "semantic_search"}, "timestamp": 1748932648.248218, "ttl": 3600, "access_count": 0}