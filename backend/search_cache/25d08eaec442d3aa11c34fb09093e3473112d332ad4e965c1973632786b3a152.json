{"query": "new", "video_id": 8, "method": "visual_search", "result": {"query": "new", "results": [{"timestamp": 0.0, "confidence": 70.0, "description": "Match for 'new': *   **Text (right screen):**\n    *   \"The new way to cloud...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_0_0.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 85.0, "confidence": 70.0, "description": "Match for 'new': Colors:\n-   **White**: Dominant background color for the left overlay panel; primary color for most text on the digital screen (\"The new cloud...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_1_85.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 165.0, "confidence": 70.0, "description": "Match for 'new': Text:\n- Left side (on white background):\n    - \"Recap\" (large font, gradient blue to green)\n    - \"Google Cloud\" (medium font, black)\n    - \"Next '25 Opening\" (medium font, black)\n    - \"Keynote in 10...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_2_165.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 245.0, "confidence": 70.0, "description": "Match for 'new': - **Crimson red:** As part of the multi-colored Google logo on the right screen (\"w\" in \"new way\") and the Google Cloud logo in the bottom left...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_3_245.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 305.0, "confidence": 70.0, "description": "Match for 'new': - Multi-colored (Red, Yellow, Green, Blue): Integrated into the 'e' and 'w' of \"new\" on the large screen, representing the Google Cloud logo colors...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_4_305.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 375.0, "confidence": 70.0, "description": "Match for 'new': \",\n      \"gender\": \"Male\",\n      \"age_group\": \"Adult (middle-aged/older)\",\n      \"clothing\": \"Dark suit, white shirt, dark bow tie\"\n    }\n  ],\n  \"Colors\": [\n    \"Pure white (background for left text p...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_5_375.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 435.0, "confidence": 70.0, "description": "Match for 'new': Colors:\n- Pure white (left panel background, text on screen)\n- Gradient royal blue to lime green (for \"Recap\" text)\n- Deep black (background of the large screen, text on left panel, presenter's bow ti...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_6_435.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 505.0, "confidence": 70.0, "description": "Match for 'new': \"\n]\nColors: [\n  \"Dominant pure white (left background)\",\n  \"Deep black (presentation screen background)\",\n  \"Bright white (text on screen and left panel)\",\n  \"Vibrant yellow (upper right abstract bloc...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_7_505.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 560.0, "confidence": 70.0, "description": "Match for 'new': Objects: [Large display screen, Stage platform, Illuminated vertical backdrop panels (behind the speaker), Spotlights/lighting fixtures (small white dots on the ceiling above the stage), Google Cloud ...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_8_560.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "clips": [{"start_time": 0.0, "end_time": 0.0, "confidence": 70.0, "description": "Clip from 0.0s to 0.0s: Match for 'new': *   **Text (right screen):**\n    *   \"The new way to cloud...", "frame_count": 1, "frames": [{"timestamp": 0.0, "confidence": 70.0, "description": "Match for 'new': *   **Text (right screen):**\n    *   \"The new way to cloud...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_0_0.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 85.0, "end_time": 85.0, "confidence": 70.0, "description": "Clip from 85.0s to 85.0s: Match for 'new': Colors:\n-   **White**: Dominant background color for the left overlay panel; primary color for most text on the digital screen (\"The new cloud...", "frame_count": 1, "frames": [{"timestamp": 85.0, "confidence": 70.0, "description": "Match for 'new': Colors:\n-   **White**: Dominant background color for the left overlay panel; primary color for most text on the digital screen (\"The new cloud...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_1_85.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 165.0, "end_time": 165.0, "confidence": 70.0, "description": "Clip from 165.0s to 165.0s: Match for 'new': Text:\n- Left side (on white background):\n    - \"Recap\" (large font, gradient blue to green)\n    - \"Google Cloud\" (medium font, black)\n    - \"Next '25 Opening\" (medium font, black)\n    - \"Keynote in 10...", "frame_count": 1, "frames": [{"timestamp": 165.0, "confidence": 70.0, "description": "Match for 'new': Text:\n- Left side (on white background):\n    - \"Recap\" (large font, gradient blue to green)\n    - \"Google Cloud\" (medium font, black)\n    - \"Next '25 Opening\" (medium font, black)\n    - \"Keynote in 10...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_2_165.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 245.0, "end_time": 245.0, "confidence": 70.0, "description": "Clip from 245.0s to 245.0s: Match for 'new': - **Crimson red:** As part of the multi-colored Google logo on the right screen (\"w\" in \"new way\") and the Google Cloud logo in the bottom left...", "frame_count": 1, "frames": [{"timestamp": 245.0, "confidence": 70.0, "description": "Match for 'new': - **Crimson red:** As part of the multi-colored Google logo on the right screen (\"w\" in \"new way\") and the Google Cloud logo in the bottom left...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_3_245.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 305.0, "end_time": 305.0, "confidence": 70.0, "description": "Clip from 305.0s to 305.0s: Match for 'new': - Multi-colored (Red, Yellow, Green, Blue): Integrated into the 'e' and 'w' of \"new\" on the large screen, representing the Google Cloud logo colors...", "frame_count": 1, "frames": [{"timestamp": 305.0, "confidence": 70.0, "description": "Match for 'new': - Multi-colored (Red, Yellow, Green, Blue): Integrated into the 'e' and 'w' of \"new\" on the large screen, representing the Google Cloud logo colors...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_4_305.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}], "total_results": 9, "direct_answer": "Found 9 instances of 'new' in the video", "query_type": null, "summary": null, "processing_method": "semantic_search"}, "timestamp": 1748932643.40975, "ttl": 3600, "access_count": 0}