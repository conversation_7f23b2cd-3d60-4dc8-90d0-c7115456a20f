{"query": "person", "video_id": 1, "method": "visual_search", "result": {"query": "person", "results": [{"timestamp": 426.0, "confidence": 70.0, "description": "Match for 'person': *   **Person's Attire:** Dark charcoal grey/black suit, pure white shirt, black bow tie...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_5_426.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "clips": [{"start_time": 426.0, "end_time": 426.0, "confidence": 70.0, "description": "Clip from 426.0s to 426.0s: Match for 'person': *   **Person's Attire:** Dark charcoal grey/black suit, pure white shirt, black bow tie...", "frame_count": 1, "frames": [{"timestamp": 426.0, "confidence": 70.0, "description": "Match for 'person': *   **Person's Attire:** Dark charcoal grey/black suit, pure white shirt, black bow tie...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_5_426.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}], "total_results": 1, "direct_answer": "Found 1 instances of 'person' in the video", "query_type": null, "summary": null, "processing_method": "semantic_search"}, "timestamp": 1748934353.454257, "ttl": 3600, "access_count": 0}