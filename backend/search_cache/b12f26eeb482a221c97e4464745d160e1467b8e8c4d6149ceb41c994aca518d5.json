{"query": "goal", "video_id": 9, "method": "visual_search", "result": {"query": "goal", "results": [{"timestamp": 245.0, "confidence": 70.0, "description": "Match for 'goal': -   <PERSON><PERSON><PERSON> is shown in an intense, highly emotional state, possibly reacting to a goal or a critical moment...", "frame_path": "/api/frames/frame_Ar4LrKcwh3w_4_245.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "clips": [{"start_time": 245.0, "end_time": 245.0, "confidence": 70.0, "description": "Clip from 245.0s to 245.0s: Match for 'goal': -   <PERSON><PERSON><PERSON> is shown in an intense, highly emotional state, possibly reacting to a goal or a critical moment...", "frame_count": 1, "frames": [{"timestamp": 245.0, "confidence": 70.0, "description": "Match for 'goal': -   <PERSON><PERSON><PERSON> is shown in an intense, highly emotional state, possibly reacting to a goal or a critical moment...", "frame_path": "/api/frames/frame_Ar4LrKcwh3w_4_245.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}], "total_results": 1, "direct_answer": "Found 1 instances of 'goal' in the video", "query_type": null, "summary": null, "processing_method": "semantic_search"}, "timestamp": 1748935687.301408, "ttl": 3600, "access_count": 0}