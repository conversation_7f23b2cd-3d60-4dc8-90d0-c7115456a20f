{"query": "text", "video_id": 8, "method": "visual_search", "result": {"query": "text", "results": [{"timestamp": 0.0, "confidence": 70.0, "description": "Match for 'text': **Objects:**\n*   Large digital display screen (dominant on the right side) showing white text on a black background...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_0_0.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 85.0, "confidence": 70.0, "description": "Match for 'text': Objects:\n-   Large digital projection screen (dominant feature on the right side, serving as a backdrop)\n-   Stage platform (where the speaker is standing)\n-   Multiple tall, rectangular backlit panel...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_1_85.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 165.0, "confidence": 70.0, "description": "Match for 'text': - A large area of solid white background on the left side of the frame, serving as a canvas for text and logos...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_2_165.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 245.0, "confidence": 70.0, "description": "Match for 'text': - **Deep black:** For the main body text on the left panel, the background of the right screen panel, and the speaker's suit/bowtie...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_3_245.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 305.0, "confidence": 70.0, "description": "Match for 'text': Objects:\n- Large rectangular projection screen displaying text and a stylized word...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_4_305.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 375.0, "confidence": 70.0, "description": "Match for 'text': ```json\n{\n  \"Objects\": [\n    \"Large presentation screen (dominant right side)\",\n    \"Male presenter/speaker on stage\",\n    \"Stage lighting panels (vertical, illuminated)\",\n    \"Abstract background sha...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_5_375.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 435.0, "confidence": 70.0, "description": "Match for 'text': Colors:\n- Pure white (left panel background, text on screen)\n- Gradient royal blue to lime green (for \"Recap\" text)\n- Deep black (background of the large screen, text on left panel, presenter's bow ti...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_6_435.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 505.0, "confidence": 70.0, "description": "Match for 'text': \"\n]\nColors: [\n  \"Dominant pure white (left background)\",\n  \"Deep black (presentation screen background)\",\n  \"Bright white (text on screen and left panel)\",\n  \"Vibrant yellow (upper right abstract bloc...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_7_505.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}, {"timestamp": 560.0, "confidence": 70.0, "description": "Match for 'text': Objects: [Large display screen, Stage platform, Illuminated vertical backdrop panels (behind the speaker), Spotlights/lighting fixtures (small white dots on the ceiling above the stage), Google Cloud ...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_8_560.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "clips": [{"start_time": 0.0, "end_time": 0.0, "confidence": 70.0, "description": "Clip from 0.0s to 0.0s: Match for 'text': **Objects:**\n*   Large digital display screen (dominant on the right side) showing white text on a black background...", "frame_count": 1, "frames": [{"timestamp": 0.0, "confidence": 70.0, "description": "Match for 'text': **Objects:**\n*   Large digital display screen (dominant on the right side) showing white text on a black background...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_0_0.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 85.0, "end_time": 85.0, "confidence": 70.0, "description": "Clip from 85.0s to 85.0s: Match for 'text': Objects:\n-   Large digital projection screen (dominant feature on the right side, serving as a backdrop)\n-   Stage platform (where the speaker is standing)\n-   Multiple tall, rectangular backlit panel...", "frame_count": 1, "frames": [{"timestamp": 85.0, "confidence": 70.0, "description": "Match for 'text': Objects:\n-   Large digital projection screen (dominant feature on the right side, serving as a backdrop)\n-   Stage platform (where the speaker is standing)\n-   Multiple tall, rectangular backlit panel...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_1_85.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 165.0, "end_time": 165.0, "confidence": 70.0, "description": "Clip from 165.0s to 165.0s: Match for 'text': - A large area of solid white background on the left side of the frame, serving as a canvas for text and logos...", "frame_count": 1, "frames": [{"timestamp": 165.0, "confidence": 70.0, "description": "Match for 'text': - A large area of solid white background on the left side of the frame, serving as a canvas for text and logos...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_2_165.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 245.0, "end_time": 245.0, "confidence": 70.0, "description": "Clip from 245.0s to 245.0s: Match for 'text': - **Deep black:** For the main body text on the left panel, the background of the right screen panel, and the speaker's suit/bowtie...", "frame_count": 1, "frames": [{"timestamp": 245.0, "confidence": 70.0, "description": "Match for 'text': - **Deep black:** For the main body text on the left panel, the background of the right screen panel, and the speaker's suit/bowtie...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_3_245.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}, {"start_time": 305.0, "end_time": 305.0, "confidence": 70.0, "description": "Clip from 305.0s to 305.0s: Match for 'text': Objects:\n- Large rectangular projection screen displaying text and a stylized word...", "frame_count": 1, "frames": [{"timestamp": 305.0, "confidence": 70.0, "description": "Match for 'text': Objects:\n- Large rectangular projection screen displaying text and a stylized word...", "frame_path": "/api/frames/frame_dwgmfSOZNoQ_4_305.jpg", "summary": null, "objects_detected": null, "people_count": null, "detailed_analysis": null, "image_similarity": null, "text_similarity": null, "keyword_matches": null}], "thumbnail_url": null}], "total_results": 9, "direct_answer": "Found 9 instances of 'text' in the video", "query_type": null, "summary": null, "processing_method": "semantic_search"}, "timestamp": 1748932654.968586, "ttl": 3600, "access_count": 0}