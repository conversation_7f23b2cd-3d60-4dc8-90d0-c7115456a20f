fastapi>=0.104.0
uvicorn>=0.24.0
python-multipart>=0.0.6
youtube-transcript-api>=0.6.0
google-generativeai>=0.3.0
opencv-python>=4.8.0
pillow>=10.0.0
numpy>=1.24.0
chromadb>=0.4.0
python-dotenv>=1.0.0
httpx>=0.25.0
pydantic>=2.5.0
pydantic-settings>=2.0.0
sqlalchemy>=2.0.0
alembic>=1.13.0
yt-dlp>=2023.12.30
redis>=5.0.0
scikit-learn>=1.3.0

# Enhanced multimodal capabilities - Python 3.13 compatible versions
torch>=2.1.0,<2.5.0  # Latest stable version with Python 3.13 support
torchvision>=0.16.0,<0.20.0  # Compatible with torch version
sentence-transformers>=2.2.0  # Now enabled with proper torch version
transformers>=4.35.0  # Latest version with Python 3.13 support
open-clip-torch>=2.24.0  # Alternative to clip-by-openai with better compatibility
accelerate>=0.24.0  # For optimized model loading
huggingface-hub>=0.19.0  # For model downloads
