import os
from typing import List
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """Application settings"""

    # API Keys
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")

    # Database
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./videochat.db")

    # Redis
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")

    # Application
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:8081",
        "http://localhost:8082",
        "http://localhost:8083",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:8081",
        "http://127.0.0.1:8082",
        "http://127.0.0.1:8083"
    ]

    # Video Processing
    MAX_VIDEO_SIZE_MB: int = int(os.getenv("MAX_VIDEO_SIZE_MB", "500"))
    FRAME_EXTRACTION_INTERVAL: int = int(os.getenv("FRAME_EXTRACTION_INTERVAL", "3"))  # Reduced from 5 to 3 seconds for better coverage
    MAX_VIDEO_DURATION_SECONDS: int = int(os.getenv("MAX_VIDEO_DURATION_SECONDS", "21600"))  # Increased to 6 hours (21600 seconds)
    MAX_FRAMES_PER_VIDEO: int = int(os.getenv("MAX_FRAMES_PER_VIDEO", "1000"))  # Increased to match GEMINI_MAX_VIDEO_FRAMES
    UPLOAD_DIR: str = "./uploads"

    # Vector Database
    CHROMA_PERSIST_DIRECTORY: str = os.getenv("CHROMA_PERSIST_DIRECTORY", "./chroma_db")

    # Redis Cache (Optional)
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "3600"))  # 1 hour default

    # Gemini Settings
    # Options: "gemini-2.5-flash", "gemini-2.5-pro-preview-0506", "gemini-2.0-flash-exp"
    GEMINI_MODEL: str = os.getenv("GEMINI_MODEL", "gemini-2.5-flash-preview-05-20")  # Latest Gemini 2.5 with video understanding
    GEMINI_TEMPERATURE: float = 0.7
    GEMINI_MAX_TOKENS: int = 2048
    
    # Video processing settings for Gemini 2.5
    GEMINI_VIDEO_RESOLUTION: str = os.getenv("GEMINI_VIDEO_RESOLUTION", "low")  # "low" enables 6-hour video support
    GEMINI_MAX_VIDEO_FRAMES: int = int(os.getenv("GEMINI_MAX_VIDEO_FRAMES", "1000"))  # Increased from 256 for better search accuracy (max 7200)
    
    # Scene Detection & Keyframe Settings
    SCENE_KEYFRAME_PERCENTAGE: float = float(os.getenv("SCENE_KEYFRAME_PERCENTAGE", "0.04"))  # 4% keyframes (3-5% range)
    SCENE_CHANGE_THRESHOLD: float = float(os.getenv("SCENE_CHANGE_THRESHOLD", "0.3"))
    SCENE_HISTOGRAM_BINS: int = int(os.getenv("SCENE_HISTOGRAM_BINS", "64"))
    SCENE_MOTION_THRESHOLD: float = float(os.getenv("SCENE_MOTION_THRESHOLD", "0.2"))
    
    # CLIP + Optical Flow Settings
    ENABLE_CLIP_EMBEDDINGS: bool = os.getenv("ENABLE_CLIP_EMBEDDINGS", "True").lower() == "true"
    ENABLE_OPTICAL_FLOW: bool = os.getenv("ENABLE_OPTICAL_FLOW", "True").lower() == "true"
    CLIP_MODEL_NAME: str = os.getenv("CLIP_MODEL_NAME", "openai/clip-vit-base-patch32")
    OPTICAL_FLOW_METHOD: str = os.getenv("OPTICAL_FLOW_METHOD", "farneback")  # "farneback" or "dense"
    
    # API Resilience Settings
    GEMINI_MAX_RETRIES: int = int(os.getenv("GEMINI_MAX_RETRIES", "3"))
    GEMINI_INITIAL_BACKOFF: float = float(os.getenv("GEMINI_INITIAL_BACKOFF", "1.0"))  # seconds
    GEMINI_MAX_BACKOFF: float = float(os.getenv("GEMINI_MAX_BACKOFF", "60.0"))  # seconds
    GEMINI_BACKOFF_MULTIPLIER: float = float(os.getenv("GEMINI_BACKOFF_MULTIPLIER", "2.0"))
    CIRCUIT_BREAKER_THRESHOLD: int = int(os.getenv("CIRCUIT_BREAKER_THRESHOLD", "5"))  # failures before opening
    CIRCUIT_BREAKER_TIMEOUT: int = int(os.getenv("CIRCUIT_BREAKER_TIMEOUT", "300"))  # seconds before retry
    
    # Enhanced Search Settings
    CONFIDENCE_THRESHOLD: float = float(os.getenv("CONFIDENCE_THRESHOLD", "0.3"))
    SIMILARITY_METRIC: str = os.getenv("SIMILARITY_METRIC", "cosine")  # "cosine", "euclidean", "dot"
    MAX_SEARCH_RESULTS: int = int(os.getenv("MAX_SEARCH_RESULTS", "50"))
    CLIP_WEIGHT: float = float(os.getenv("CLIP_WEIGHT", "0.6"))  # Weight for CLIP vs text embeddings
    OPTICAL_FLOW_WEIGHT: float = float(os.getenv("OPTICAL_FLOW_WEIGHT", "0.2"))  # Weight for motion features

    class Config:
        env_file = ".env"
        extra = "ignore"  # Allow extra fields in .env file

settings = Settings()
