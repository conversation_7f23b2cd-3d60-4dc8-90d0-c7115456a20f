from fastapi import APIRouter, HTTPException, Depends, Request, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional, Dict
import logging
import os
from collections import defaultdict

from app.core.database import get_db
from app.core.config import settings
from app.models.video import Video, VideoFrame
from app.services.gemini_service import GeminiService
from app.services.thumbnail_service import ThumbnailService
from app.services.unified_search_service import get_unified_search_service, UnifiedSearchService
from app.services.native_video_search import (
    NativeVideoSearchService,
    count_occurrences,
    find_color_objects,
    find_text,
    find_scenes
)

logger = logging.getLogger(__name__)

# Initialize unified search service
try:
    unified_search_service = get_unified_search_service()
    UNIFIED_SEARCH_AVAILABLE = unified_search_service.initialized
    logger.info("✅ Unified Search Service available")
except Exception as e:
    logger.error(f"Failed to initialize Unified Search Service: {e}")
    UNIFIED_SEARCH_AVAILABLE = False
    unified_search_service = None
router = APIRouter()

class VisualSearchRequest(BaseModel):
    video_id: int
    query: str
    max_results: int = 10

class SearchResult(BaseModel):
    timestamp: float
    confidence: float
    description: str
    frame_path: str
    summary: Optional[str] = None  # Concise answer
    objects_detected: Optional[List[str]] = None
    people_count: Optional[int] = None
    detailed_analysis: Optional[str] = None
    image_similarity: Optional[float] = None
    text_similarity: Optional[float] = None
    keyword_matches: Optional[List[str]] = None
    
class ClipResult(BaseModel):
    start_time: float
    end_time: float
    confidence: float
    description: str
    frame_count: int
    frames: List[SearchResult]
    thumbnail_url: Optional[str] = None

class VisualSearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    clips: List[ClipResult]
    total_results: int
    direct_answer: Optional[str] = None  # For counting and specific queries
    query_type: Optional[str] = None  # 'counting' | 'object_detection' | 'scene_analysis' | 'general'
    summary: Optional[str] = None
    processing_method: Optional[str] = None  # 'batch_video' | 'hybrid_search' | 'vector_search' | 'semantic_search'

def calculate_confidence_from_distance(distance: float, distance_metric: str = "cosine") -> float:
    """
    Convert distance to confidence score (0-100) with proper normalization
    
    Args:
        distance: Distance value from vector search
        distance_metric: Type of distance metric used
    
    Returns:
        Confidence score between 0 and 100
    """
    if distance_metric == "cosine":
        # ChromaDB cosine distance is typically [0, 1] where 0 is identical
        # Apply exponential decay for more realistic scoring
        if distance <= 0.1:  # Very close matches
            confidence = 95.0 - (distance * 50)  # 95% to 90%
        elif distance <= 0.3:  # Good matches
            confidence = 90.0 - ((distance - 0.1) * 250)  # 90% to 40%
        elif distance <= 0.6:  # Moderate matches
            confidence = 40.0 - ((distance - 0.3) * 100)  # 40% to 10%
        else:  # Poor matches
            confidence = max(5.0, 10.0 - ((distance - 0.6) * 12.5))  # 10% to 5%
    elif distance_metric == "euclidean":
        # For euclidean, normalize assuming range [0, 1.414]
        similarity = max(0, 1 - (distance / 1.414))
        confidence = round(similarity * 90, 1)  # Cap at 90% for euclidean
    else:
        # Conservative fallback
        confidence = max(5.0, 50.0 - (distance * 25))
    
    return min(100.0, max(5.0, round(confidence, 1)))

def group_frames_into_clips(results: List[SearchResult], threshold_seconds: float = 5.0, video_path: str = None) -> List[ClipResult]:
    """
    Group consecutive frames into clips based on temporal proximity
    
    Args:
        results: List of search results sorted by timestamp
        threshold_seconds: Maximum gap between frames to consider them part of the same clip
    
    Returns:
        List of clips with aggregated confidence
    """
    if not results:
        return []
    
    # Sort by timestamp
    sorted_results = sorted(results, key=lambda x: x.timestamp)
    
    clips = []
    current_clip_frames = [sorted_results[0]]
    
    for i in range(1, len(sorted_results)):
        current_frame = sorted_results[i]
        last_frame = current_clip_frames[-1]
        
        # Check if this frame is close enough to be part of the same clip
        if current_frame.timestamp - last_frame.timestamp <= threshold_seconds:
            current_clip_frames.append(current_frame)
        else:
            # Create clip from accumulated frames
            if current_clip_frames:
                clips.append(_create_clip_from_frames(current_clip_frames, video_path))
            current_clip_frames = [current_frame]
    
    # Don't forget the last clip
    if current_clip_frames:
        clips.append(_create_clip_from_frames(current_clip_frames, video_path))
    
    return clips

def _create_clip_from_frames(frames: List[SearchResult], video_path: str = None) -> ClipResult:
    """Create a clip from a group of frames with thumbnail generation"""
    start_time = frames[0].timestamp
    end_time = frames[-1].timestamp
    
    # Calculate average confidence with temporal smoothing
    confidences = [f.confidence for f in frames]
    avg_confidence = sum(confidences) / len(confidences)
    
    # Use the description from the highest confidence frame
    best_frame = max(frames, key=lambda f: f.confidence)
    
    # Generate thumbnail for this clip
    thumbnail_url = None
    if video_path and os.path.exists(video_path):
        try:
            logger.info(f"🖼️ Generating thumbnail for clip {start_time}s-{end_time}s from video: {video_path}")
            thumbnail_service = ThumbnailService()
            # Use the middle timestamp of the clip for thumbnail
            thumbnail_timestamp = (start_time + end_time) / 2
            thumbnail_path = thumbnail_service.generate_thumbnail(
                video_path, 
                thumbnail_timestamp, 
                'medium',  # Use medium size for clips
                add_timestamp=True
            )
            if thumbnail_path and os.path.exists(thumbnail_path):
                from pathlib import Path
                thumbnail_url = f"/api/thumbnails/{Path(thumbnail_path).name}"
                logger.info(f"✅ Thumbnail generated successfully: {thumbnail_url}")
            else:
                logger.warning(f"❌ Thumbnail generation returned empty path or file doesn't exist")
        except Exception as e:
            logger.error(f"❌ Failed to generate thumbnail for clip at {start_time}s: {e}", exc_info=True)
    else:
        logger.warning(f"❌ Video path invalid or doesn't exist: {video_path}")
    
    return ClipResult(
        start_time=start_time,
        end_time=end_time,
        confidence=round(avg_confidence, 1),
        description=f"Clip from {start_time:.1f}s to {end_time:.1f}s: {best_frame.description}",
        frame_count=len(frames),
        frames=frames,
        thumbnail_url=thumbnail_url
    )

@router.post("/visual", response_model=VisualSearchResponse)
async def visual_search(
    request: Request,
    search_request: VisualSearchRequest,
    db: Session = Depends(get_db)
):
    """Unified Visual Search using optimal search strategy"""
    try:
        # Initialize cache service
        from app.services.search_cache_service import SearchCacheService
        cache_service = SearchCacheService()

        # Check cache first
        cached_result = cache_service.get_cached_result(
            query=search_request.query,
            video_id=search_request.video_id,
            search_method="fast_hybrid_search",
            additional_params={"max_results": search_request.max_results}
        )

        if cached_result:
            logger.info(f"🎯 Returning cached result for query: {search_request.query}")
            return VisualSearchResponse(**cached_result)

        # Get video
        video = db.query(Video).filter(Video.id == search_request.video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        logger.info(f"🚀 UNIFIED SEARCH for query: '{search_request.query}'")
        logger.info(f"Video ID: {video.id}, Title: {video.title}")

        # Check if unified search is available
        if not UNIFIED_SEARCH_AVAILABLE:
            raise HTTPException(
                status_code=503,
                detail="Unified Search service not available. Please ensure dependencies are installed."
            )

        # Perform unified search
        logger.info(f"🔍 Performing unified search for: '{search_request.query}'")
        try:
            search_results = await unified_search_service.search(
                query=search_request.query,
                video_id=search_request.video_id,
                limit=search_request.max_results,
                search_type="auto"  # Let the service decide the best approach
            )

            # Convert results to SearchResult format
            results = []
            for result in search_results.get('results', []):
                # Extract confidence score
                confidence = result.get('relevance_score', 0.0)
                if confidence == 0.0:
                    confidence = result.get('confidence', 0.8)
                
                results.append(SearchResult(
                    timestamp=result.get('timestamp', 0),
                    confidence=confidence,
                    description=result.get('description', ''),
                    frame_path=result.get('frame_path', ''),
                    summary=result.get('description', '')[:100] + "..." if len(result.get('description', '')) > 100 else result.get('description', ''),
                    objects_detected=result.get('metadata', {}).get('objects', [])[:5],
                    detailed_analysis=result.get('description', ''),
                    image_similarity=result.get('metadata', {}).get('image_similarity', 0.0),
                    text_similarity=result.get('metadata', {}).get('text_similarity', 0.0),
                    keyword_matches=result.get('metadata', {}).get('keyword_matches', [])
                ))

            # Determine query type and generate direct answer
            query_lower = search_request.query.lower()
            direct_answer = None
            query_type = "general"

            if any(word in query_lower for word in ['how many', 'count', 'number of']):
                query_type = "counting"
                if 'people' in query_lower or 'person' in query_lower:
                    direct_answer = f"{len(results)} instances of people detected in video"
                else:
                    direct_answer = f"{len(results)} instances found matching '{search_request.query}'"
            elif any(word in query_lower for word in ['microphone', 'mic', 'equipment', 'object']):
                query_type = "object_detection"
                direct_answer = f"Object '{search_request.query}' detected in {len(results)} instances"
            elif any(word in query_lower for word in ['background', 'scene', 'setting', 'color']):
                query_type = "scene_analysis"
                direct_answer = f"Scene analysis found {len(results)} relevant moments"
            else:
                direct_answer = f"Found {len(results)} instances of '{search_request.query}' in the video"

            # Group results into clips
            clips = group_frames_into_clips(results, video_path=video.file_path)
            clips.sort(key=lambda c: c.confidence, reverse=True)

            # Create response
            response = VisualSearchResponse(
                query=search_request.query,
                results=results[:search_request.max_results],
                clips=clips[:max(5, search_request.max_results // 2)],
                total_results=len(results),
                direct_answer=direct_answer,
                query_type=query_type,
                processing_method=search_results.get('method', 'unified_search')
            )

            # Cache the result
            cache_service.cache_result(
                query=search_request.query,
                video_id=search_request.video_id,
                search_method="unified_search",
                result=response.model_dump(),
                additional_params={"max_results": search_request.max_results}
            )

            logger.info(f"✅ Unified search completed: {len(results)} results for '{search_request.query}'")
            return response

        except Exception as e:
            logger.error(f"Unified search failed: {e}")
            raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in fast hybrid visual search: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/migrate-to-unified-search")
async def migrate_to_unified_search(
    db: Session = Depends(get_db)
):
    """Migrate existing frame data to Unified Search index"""
    try:
        logger.info("🔄 Starting migration to Unified Search...")

        # Check if unified search is available
        if not UNIFIED_SEARCH_AVAILABLE:
            raise HTTPException(status_code=503, detail="Unified Search not available")

        if not unified_search_service or not unified_search_service.initialized:
            raise HTTPException(status_code=503, detail="Unified Search Service not available")

        # Get all frames with descriptions
        frames = db.query(VideoFrame).filter(VideoFrame.description.isnot(None)).all()
        logger.info(f"📊 Found {len(frames)} frames with descriptions")

        if not frames:
            return {
                "message": "No frames to migrate",
                "migrated_count": 0,
                "total_frames": 0
            }

        # Prepare batch data
        frames_data = []
        for frame in frames:
            # Clean up description (remove JSON formatting if present)
            description = frame.description
            if description.startswith('```json'):
                # Extract meaningful content from JSON
                try:
                    import json
                    start = description.find('{')
                    end = description.rfind('}')
                    if start != -1 and end != -1:
                        json_data = json.loads(description[start:end+1])
                        # Extract objects or description
                        if 'Objects' in json_data and isinstance(json_data['Objects'], list):
                            description = ', '.join(json_data['Objects'][:5])  # Top 5 objects
                        elif 'description' in json_data:
                            description = json_data['description']
                except:
                    # If JSON parsing fails, use original
                    pass

            frames_data.append({
                'frame_id': f'frame_{frame.id}',
                'description': description,
                'timestamp': frame.timestamp,
                'frame_path': frame.frame_path,
                'video_id': frame.video_id,
                'metadata': {
                    'frame_id': frame.id,
                    'video_id': frame.video_id,
                    'timestamp': frame.timestamp,
                    'frame_path': frame.frame_path
                }
            })

        # Batch add frames
        logger.info(f"🚀 Adding {len(frames_data)} frames to Unified Search...")
        success_count = await unified_search_service.batch_add_frames(frames_data)

        # Get stats
        stats = unified_search_service.get_stats()

        logger.info(f"✅ Successfully migrated {success_count}/{len(frames_data)} frames")

        return {
            "message": f"Successfully migrated {success_count} frames to Unified Search",
            "migrated_count": success_count,
            "total_frames": len(frames_data),
            "stats": stats
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise HTTPException(status_code=500, detail=f"Migration failed: {str(e)}")

def _convert_frame_path_to_url(frame_path: str) -> str:
    """Convert local frame path to servable URL"""
    if not frame_path:
        return ""
    
    # Convert uploads/frames/filename.jpg to /api/frames/filename.jpg
    if frame_path.startswith("uploads/frames/"):
        filename = frame_path.replace("uploads/frames/", "")
        return f"/api/frames/{filename}"
    elif frame_path.startswith("/uploads/frames/"):
        filename = frame_path.replace("/uploads/frames/", "")
        return f"/api/frames/{filename}"
    elif not frame_path.startswith("/api/"):
        # If it's just a filename, assume it's in frames directory
        from pathlib import Path
        filename = Path(frame_path).name
        return f"/api/frames/{filename}"
    
    return frame_path

def _validate_semantic_match(query: str, description: str, exact_matches: int, semantic_matches: int) -> bool:
    """Simple validation based on exact matches only - no pre-mappings"""
    if not description:
        return False

    query_words = query.lower().split()
    
    # For compound queries (multiple words), require ALL words to match
    if len(query_words) > 1:
        # All words must be found
        if exact_matches < len(query_words):
            return False
            
        # Check proximity for compound queries
        import re
        description_lower = description.lower()
        
        # Find positions of all query words
        word_positions = []
        for word in query_words:
            pattern = r'\b' + re.escape(word) + r'\b'
            match = re.search(pattern, description_lower)
            if match:
                word_positions.append(match.start())
        
        # If words are found, check they're reasonably close (within 50 chars)
        if len(word_positions) == len(query_words):
            word_positions.sort()
            max_distance = word_positions[-1] - word_positions[0]
            if max_distance > 50:  # Words too far apart
                return False
        
        return True
    
    # For single-word queries, just need exact match
    return exact_matches > 0

async def _enhanced_semantic_search(query: str, frames: List[VideoFrame], video_path: str = None) -> List[SearchResult]:
    """Dynamic semantic search based on actual video content"""
    results = []
    query_lower = query.lower()
    query_words = set(query_lower.split())
    
    # Initialize dynamic content analyzer
    from app.services.dynamic_content_analyzer import DynamicContentAnalyzer
    analyzer = DynamicContentAnalyzer()
    
    # Analyze video content dynamically
    frame_data = [
        {'description': f.description, 'timestamp': f.timestamp} 
        for f in frames if f.description
    ]
    
    video_content = analyzer.analyze_video_content(frame_data)
    
    logger.info(f"📊 Video Content Analysis:")
    logger.info(f"   Top Objects: {list(video_content.objects.keys())[:5]}")
    logger.info(f"   Top Colors: {list(video_content.colors.keys())[:3]}")
    logger.info(f"   Top Actions: {list(video_content.actions.keys())[:3]}")
    logger.info(f"   Suggested Queries: {video_content.suggested_queries[:5]}")
    
    # Validate query against actual video content
    is_relevant, relevance_score, relevance_msg = analyzer.validate_query_against_content(query, video_content)
    
    if not is_relevant:
        logger.info(f"❌ Query '{query}' not relevant to video content: {relevance_msg}")
        return []  # Return empty results for irrelevant queries
    
    logger.info(f"✅ Query relevance: {relevance_score:.2f} - {relevance_msg}")

    logger.info(f"🔍 Processing {len(frames)} frames for query: '{query}'")

    for frame in frames:
        confidence = 0.0
        description = ""
        
        if frame.description:
            description_lower = frame.description.lower()
            
            # Simple exact matching with word boundaries
            import re
            
            # Count exact word matches
            exact_matches = 0
            match_positions = []
            
            for word in query_words:
                # Use word boundaries for exact matching
                pattern = r'\b' + re.escape(word) + r'\b'
                matches = list(re.finditer(pattern, description_lower, re.IGNORECASE))
                if matches:
                    exact_matches += 1
                    match_positions.extend([m.start() for m in matches])
            
            # For compound queries, check proximity
            if len(query_words) > 1 and exact_matches == len(query_words):
                # Check if all words appear within reasonable proximity
                if match_positions:
                    match_positions.sort()
                    max_distance = match_positions[-1] - match_positions[0]
                    # If words are too far apart (more than 100 chars), reduce confidence
                    if max_distance > 100:
                        exact_matches = exact_matches * 0.7
            
            # Calculate confidence based on matches
            if exact_matches > 0:
                # For exact matches, calculate confidence based on match ratio
                match_ratio = exact_matches / len(query_words) if query_words else 0
                
                # Base confidence from match ratio
                confidence = match_ratio * 70  # Max 70% for perfect match
                
                # Boost confidence if query appears multiple times
                if exact_matches > len(query_words):
                    confidence += 10
                
                # Apply relevance score multiplier
                confidence = confidence * relevance_score
                
                # Ensure reasonable bounds
                confidence = min(85.0, max(15.0, confidence))

                # Validate the match
                is_valid_match = _validate_semantic_match(query, frame.description, exact_matches, 0)
                
                if not is_valid_match:
                    continue
                
                # Extract relevant portion of description
                sentences = frame.description.split('.')
                relevant_sentence = ""
                for sentence in sentences:
                    sentence_lower = sentence.lower()
                    # Check if any query word appears in this sentence
                    if any(re.search(r'\b' + re.escape(word) + r'\b', sentence_lower, re.IGNORECASE) for word in query_words):
                        relevant_sentence = sentence.strip()
                        break

                if not relevant_sentence and sentences:
                    relevant_sentence = sentences[0].strip()

                description = f"Match for '{query}': {relevant_sentence[:200]}..."
        
        # Only include frames with actual matches
        if confidence > 20.0:
            logger.debug(f"✅ Found match at {frame.timestamp:.1f}s with confidence {confidence:.1f}% for query '{query}'")
            logger.debug(f"   Exact matches: {exact_matches}")
            logger.debug(f"   Description: {description[:100]}...")

            # Generate frame thumbnail if frame_path is empty or invalid
            frame_url = _convert_frame_path_to_url(frame.frame_path) if frame.frame_path else ""
            
            # If no frame exists, try to generate a thumbnail on-demand
            if not frame_url and video_path and os.path.exists(video_path):
                try:
                    thumbnail_service = ThumbnailService()
                    thumbnail_path = thumbnail_service.generate_thumbnail(
                        video_path,
                        frame.timestamp,
                        'small',
                        add_timestamp=True
                    )
                    if thumbnail_path:
                        from pathlib import Path
                        frame_url = f"/api/thumbnails/{Path(thumbnail_path).name}"
                        logger.info(f"🖼️ Generated on-demand thumbnail for frame at {frame.timestamp}s")
                except Exception as e:
                    logger.warning(f"Failed to generate on-demand thumbnail: {e}")

            results.append(SearchResult(
                timestamp=frame.timestamp,
                confidence=round(confidence, 1),
                description=description,
                frame_path=frame_url
            ))
        elif confidence > 0:
            logger.debug(f"❌ Rejected match at {frame.timestamp:.1f}s with low confidence {confidence:.1f}% for query '{query}'")
    
    # Sort by confidence (highest first)
    results.sort(key=lambda x: x.confidence, reverse=True)
    
    # Post-process results to remove obvious false positives
    filtered_results = []
    seen_timestamps = set()
    
    for result in results:
        # Remove duplicate timestamps
        if result.timestamp in seen_timestamps:
            continue
        seen_timestamps.add(result.timestamp)
        
        # For compound queries, apply stricter filtering
        if len(query_words) >= 2:
            # Check if result confidence is too low for compound query
            if result.confidence < 30.0:  # Higher threshold for compound queries
                logger.debug(f"Filtered out low-confidence result for compound query: {result.confidence}%")
                continue
        
        filtered_results.append(result)
    
    # If we filtered out all results, provide feedback
    if len(filtered_results) == 0 and len(results) > 0:
        logger.info(f"🔍 All {len(results)} potential matches were filtered out as low confidence")

    if len(filtered_results) == 0:
        logger.info(f"🔍 No semantic matches found for query '{query}' - this is correct if the content is not in the video")
    else:
        logger.info(f"🔍 Enhanced semantic search returned {len(filtered_results)} valid results for '{query}'")

    return filtered_results


async def _direct_visual_search(video_path: str, query: str, max_results: int = 10) -> Optional[VisualSearchResponse]:
    """
    Direct, simple visual search that actually works.
    Extracts frames and analyzes them with Gemini Vision.
    """
    try:
        import cv2
        import tempfile
        
        logger.info(f"🎯 Starting direct visual search for '{query}' on {video_path}")
        
        # Open video file
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"Could not open video: {video_path}")
            return None
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        
        logger.info(f"Video: {duration:.1f}s, {fps:.1f} FPS, {total_frames} frames")
        
        # Extract frames every 5 seconds for analysis
        interval_seconds = 5
        frame_interval = int(fps * interval_seconds)
        
        results = []
        clips = []
        
        # Initialize Gemini service
        try:
            from app.services.gemini_service import GeminiService
            gemini_service = GeminiService()
        except Exception as e:
            logger.error(f"Could not initialize Gemini service: {e}")
            return None
        
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Process frame at intervals
            if frame_count % frame_interval == 0:
                timestamp = frame_count / fps
                
                # Save frame to temporary file
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                    cv2.imwrite(tmp_file.name, frame)
                    
                    # Analyze frame with Gemini Vision
                    try:
                        analysis = await gemini_service.analyze_frame_for_search(
                            tmp_file.name, 
                            query
                        )
                        
                        if analysis.get("match", False):
                            confidence = analysis.get("confidence", 0.5)
                            description = analysis.get("description", f"Match for '{query}' found")
                            
                            # Create search result
                            result = SearchResult(
                                timestamp=timestamp,
                                confidence=confidence * 100,
                                description=description,
                                frame_path=f"/api/search/frame/{timestamp}",
                                summary=f"Found '{query}' at {timestamp:.1f}s"
                            )
                            results.append(result)
                            
                            # Create clip (5-10 seconds around the match)
                            clip_start = max(0, timestamp - 2.5)
                            clip_end = min(duration, timestamp + 7.5)
                            
                            clip = ClipResult(
                                start_time=clip_start,
                                end_time=clip_end,
                                confidence=confidence * 100,
                                description=f"Clip containing '{query}' - {description}",
                                frame_count=1,
                                frames=[result]
                            )
                            clips.append(clip)
                            
                            logger.info(f"✅ Found '{query}' at {timestamp:.1f}s with confidence {confidence:.2f}")
                        
                    except Exception as e:
                        logger.error(f"Error analyzing frame at {timestamp:.1f}s: {e}")
                        continue
                    
                    # Clean up temp file
                    try:
                        os.unlink(tmp_file.name)
                    except:
                        pass
            
            frame_count += 1
            
            # Stop if we have enough results
            if len(results) >= max_results:
                break
        
        cap.release()
        
        # Sort results by confidence
        results.sort(key=lambda x: x.confidence, reverse=True)
        clips.sort(key=lambda x: x.confidence, reverse=True)
        
        logger.info(f"🎯 Direct search completed: {len(results)} results for '{query}'")
        
        if results:
            return VisualSearchResponse(
                query=query,
                results=results[:max_results],
                clips=clips[:max_results],
                total_results=len(results),
                direct_answer=f"Found {len(results)} instances of '{query}' in the video",
                query_type="direct_visual_search",
                processing_method="direct_frame_analysis"
            )
        
        return None
        
    except Exception as e:
        logger.error(f"Error in direct visual search: {e}")
        return None

@router.get("/{video_id}/frames")
async def get_video_frames(
    video_id: int,
    limit: int = 500,  # Increased limit for longer videos
    db: Session = Depends(get_db)
):
    """Get video frames for a video"""
    # Verify video exists
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Get frames
    frames = db.query(VideoFrame)\
        .filter(VideoFrame.video_id == video_id)\
        .order_by(VideoFrame.timestamp.asc())\
        .limit(limit)\
        .all()
    
    return {
        "video_id": video_id,
        "frames": [
            {
                "id": frame.id,
                "timestamp": frame.timestamp,
                "frame_path": frame.frame_path,
                "description": frame.description,
                "objects_detected": frame.objects_detected
            }
            for frame in frames
        ]
    }

@router.post("/{video_id}/analyze-frames")
async def analyze_video_frames(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Enhanced frame analysis with direct image embeddings"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        # Get frames that haven't been analyzed
        frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.is_(None))\
            .limit(50)\
            .all()  # Increased limit for longer videos

        if not frames:
            return {
                "message": "All frames already analyzed or no frames available",
                "analyzed_count": 0
            }

        gemini_service = request.app.state.gemini_service

        analyzed_count = 0

        for frame in frames:
            try:
                # Analyze frame with Gemini Vision
                analysis = await gemini_service.analyze_frame(
                    frame.frame_path,
                    context=f"Frame from video: {video.title}"
                )

                if analysis["status"] == "success":
                    frame.description = analysis["description"]

                    # Add frame to unified search index
                    if UNIFIED_SEARCH_AVAILABLE:
                        success = await unified_search_service.add_frame(
                            frame_id=f"frame_{frame.id}",
                            description=analysis["description"],
                            timestamp=frame.timestamp,
                            frame_path=frame.frame_path,
                            video_id=video_id,
                            metadata={
                                "video_id": video_id,
                                "timestamp": frame.timestamp,
                                "frame_path": frame.frame_path
                            }
                        )
                        if success:
                            analyzed_count += 1
                    else:
                        analyzed_count += 1

            except Exception as e:
                logger.error(f"Error analyzing frame {frame.id}: {e}")
                continue

        db.commit()

        # Update video embedding status if all frames are analyzed
        total_frames = db.query(VideoFrame).filter(VideoFrame.video_id == video_id).count()
        analyzed_frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.isnot(None))\
            .count()

        if analyzed_frames == total_frames:
            video.embedding_status = "completed"
            db.commit()

        # Get service statistics
        service_stats = unified_search_service.get_stats() if UNIFIED_SEARCH_AVAILABLE else {}

        return {
            "message": f"Analyzed {analyzed_count} frames with unified search index",
            "analyzed_count": analyzed_count,
            "total_frames": len(frames),
            "video_total_frames": total_frames,
            "video_analyzed_frames": analyzed_frames,
            "unified_search_available": UNIFIED_SEARCH_AVAILABLE,
            "service_stats": service_stats
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing frames: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{video_id}/native-moment-retrieval")
async def native_moment_retrieval(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Enhanced moment retrieval using Gemini 2.5 native video analysis"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        if not hasattr(request.app.state, 'gemini_api_key'):
            raise HTTPException(status_code=500, detail="Gemini API key not configured")

        # Use Gemini service for video analysis
        gemini_service = request.app.state.gemini_service

        # Get video file path
        video_path = video.file_path
        if not video_path or not os.path.exists(video_path):
            raise HTTPException(status_code=404, detail="Video file not found")

        # Get context from transcript if available
        context = f"Title: {video.title}"
        if video.transcript:
            context += f"\nTranscript preview: {video.transcript[:1000]}"

        # Perform native moment retrieval
        moments = await gemini_service.analyze_video_moments(video_path, context)

        return {
            "video_id": video_id,
            "moments": moments,
            "analysis_method": "gemini_2.5_native",
            "total_moments": len(moments)
        }

    except Exception as e:
        logger.error(f"Error in native moment retrieval: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{video_id}/native-temporal-counting")
async def native_temporal_counting(
    request: Request,
    video_id: int,
    query: str,
    db: Session = Depends(get_db)
):
    """Enhanced temporal counting using Gemini 2.5 native video analysis"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        if not hasattr(request.app.state, 'gemini_api_key'):
            raise HTTPException(status_code=500, detail="Gemini API key not configured")

        # Use Gemini service for video analysis
        gemini_service = request.app.state.gemini_service

        # Get video file path
        video_path = video.file_path
        if not video_path or not os.path.exists(video_path):
            raise HTTPException(status_code=404, detail="Video file not found")

        # Get context from transcript if available
        context = f"Title: {video.title}"
        if video.transcript:
            context += f"\nTranscript preview: {video.transcript[:1000]}"

        # Perform native temporal counting
        result = await gemini_service.temporal_counting_analysis([{"timestamp": 0, "description": context}], query)

        return {
            "video_id": video_id,
            "query": query,
            "result": result,
            "analysis_method": "gemini_2.5_native"
        }

    except Exception as e:
        logger.error(f"Error in native temporal counting: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{video_id}/index-transcript")
async def index_video_transcript(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Index video transcript for semantic search"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.transcript:
            raise HTTPException(status_code=400, detail="Video has no transcript")
        
        # Check if unified search is available
        if not UNIFIED_SEARCH_AVAILABLE:
            raise HTTPException(
                status_code=503,
                detail="Unified search service not available"
            )
        
        # Add transcript to unified search index as a special frame
        success = await unified_search_service.add_frame(
            frame_id=f"transcript_{video_id}",
            description=video.transcript,
            timestamp=0.0,  # Transcript spans entire video
            frame_path="",   # No specific frame
            video_id=video_id,
            metadata={
                "video_id": video_id,
                "title": video.title,
                "video_type": video.video_type,
                "is_transcript": True
            }
        )
        
        if success:
            return {
                "message": "Transcript indexed successfully",
                "video_id": video_id
            }
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to index transcript"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error indexing transcript: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Advanced Gemini 2.5 Features

class TemporalCountingRequest(BaseModel):
    video_id: int
    query: str

class TemporalCountingResponse(BaseModel):
    query: str
    total_count: int
    occurrences: List[Dict]
    patterns: str
    notes: str

class MomentRetrievalResponse(BaseModel):
    video_id: int
    moments: List[Dict]
    total_moments: int

@router.post("/{video_id}/temporal-counting", response_model=TemporalCountingResponse)
async def temporal_counting_analysis(
    request: Request,
    video_id: int,
    counting_request: TemporalCountingRequest,
    db: Session = Depends(get_db)
):
    """Advanced temporal counting using Gemini 2.5 capabilities"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        # Get analyzed frames
        frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.isnot(None))\
            .order_by(VideoFrame.timestamp.asc())\
            .all()

        if not frames:
            raise HTTPException(
                status_code=400,
                detail="No analyzed frames available. Please analyze frames first."
            )

        # Prepare frame data
        frames_data = [
            {
                "timestamp": frame.timestamp,
                "description": frame.description
            }
            for frame in frames
        ]

        # Perform temporal counting analysis
        gemini_service = request.app.state.gemini_service
        result = await gemini_service.temporal_counting_analysis(
            frames_data,
            counting_request.query
        )

        return TemporalCountingResponse(
            query=counting_request.query,
            total_count=result.get("total_count", 0),
            occurrences=result.get("occurrences", []),
            patterns=result.get("patterns", ""),
            notes=result.get("notes", "")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in temporal counting analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{video_id}/moment-retrieval", response_model=MomentRetrievalResponse)
async def moment_retrieval_analysis(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Advanced moment retrieval using Gemini 2.5 capabilities"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        if not video.transcript:
            raise HTTPException(
                status_code=400,
                detail="Video transcript required for moment retrieval"
            )

        # Perform moment retrieval analysis
        gemini_service = request.app.state.gemini_service
        moments = await gemini_service.analyze_video_moments(
            video_path=video.file_path or "",
            transcript=video.transcript
        )

        return MomentRetrievalResponse(
            video_id=video_id,
            moments=moments,
            total_moments=len(moments)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in moment retrieval analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Enhanced Production Features

@router.post("/{video_id}/dual-embedding-analysis")
async def dual_embedding_analysis(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Enhanced frame analysis with dual embeddings (MiniLM + CLIP)"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        # Get frames for analysis
        frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .order_by(VideoFrame.timestamp.asc())\
            .all()

        if not frames:
            return {
                "message": "No frames available for analysis",
                "processed_count": 0
            }

        # Initialize services
        from app.services.scene_detector import SceneDetector
        scene_detector = SceneDetector()
        gemini_service = request.app.state.gemini_service

        # Scene change detection for keyframe selection (~10% of frames)
        frame_paths = [frame.frame_path for frame in frames]
        timestamps = [frame.timestamp for frame in frames]

        scene_results = scene_detector.detect_scene_changes(frame_paths, timestamps)
        keyframes = scene_detector.select_keyframes(scene_results)

        logger.info(f"Selected {len(keyframes)} keyframes from {len(frames)} total frames")

        # Process keyframes with unified search
        processed_count = 0
        for keyframe_result in keyframes:
            frame = next((f for f in frames if f.timestamp == keyframe_result.timestamp), None)
            if not frame:
                continue

            try:
                # Analyze frame if not already done
                if not frame.description:
                    analysis = await gemini_service.analyze_frame(
                        frame.frame_path,
                        context=f"Keyframe from video: {video.title}"
                    )
                    if analysis["status"] == "success":
                        frame.description = analysis["description"]

                # Add to unified search index
                if UNIFIED_SEARCH_AVAILABLE and frame.description:
                    success = await unified_search_service.add_frame(
                        frame_id=f"frame_{frame.id}",
                        description=frame.description,
                        timestamp=frame.timestamp,
                        frame_path=frame.frame_path,
                        video_id=video_id,
                        metadata={
                            "video_id": video_id,
                            "timestamp": frame.timestamp,
                            "frame_path": frame.frame_path,
                            "is_keyframe": True,
                            "scene_change_score": keyframe_result.scene_change_score,
                            "scene_id": keyframe_result.scene_id,
                            "transition_type": keyframe_result.transition_type
                        }
                    )

                    if success:
                        processed_count += 1

            except Exception as e:
                logger.error(f"Error processing keyframe {frame.id}: {e}")
                continue

        db.commit()

        # Get statistics
        scene_stats = scene_detector.get_scene_statistics(scene_results)
        service_stats = unified_search_service.get_stats() if UNIFIED_SEARCH_AVAILABLE else {}

        return {
            "message": f"Processed {processed_count} keyframes with unified search index",
            "total_frames": len(frames),
            "keyframes_selected": len(keyframes),
            "keyframe_percentage": round(len(keyframes) / len(frames) * 100, 1),
            "processed_count": processed_count,
            "scene_statistics": scene_stats,
            "service_stats": service_stats,
            "enhancement_type": "unified_search"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in dual embedding analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{video_id}/scene-keyframe-analysis")
async def scene_keyframe_analysis(
    request: Request,
    video_id: int,
    db: Session = Depends(get_db)
):
    """Enhanced scene-based keyframe analysis with CLIP + optical flow embeddings"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        if not video.file_path or not os.path.exists(video.file_path):
            raise HTTPException(status_code=400, detail="Video file not found")

        # Import and initialize the scene keyframe processor
        from app.services.scene_keyframe_processor import SceneKeyframeProcessor
        
        # Get Gemini service from app state
        gemini_service = getattr(request.app.state, 'gemini_service', None)
        
        # Initialize processor
        processor = SceneKeyframeProcessor(gemini_service)
        
        # Process video
        logger.info(f"🎬 Starting scene-based keyframe analysis for video {video_id}")
        processed_keyframes = await processor.process_video(video.file_path, video_id, db)
        
        # Store embeddings in unified search index
        if UNIFIED_SEARCH_AVAILABLE:
            try:
                stored_count = 0
                for kf in processed_keyframes:
                    # Store in unified index
                    success = await unified_search_service.add_frame(
                        frame_id=kf.frame_id,
                        description=kf.description,
                        timestamp=kf.timestamp,
                        frame_path=kf.frame_path,
                        video_id=video_id,
                        metadata={
                            "video_id": video_id,
                            "timestamp": kf.timestamp,
                            "frame_path": kf.frame_path,
                            "is_keyframe": True,
                            "scene_id": kf.scene_id,
                            "scene_change_score": kf.scene_change_score,
                            "has_clip": kf.embedding.clip_embedding is not None,
                            "has_optical_flow": kf.embedding.optical_flow_features is not None
                        }
                    )
                    if success:
                        stored_count += 1
                logger.info(f"✅ Stored {stored_count} keyframe embeddings in unified index")
            except Exception as e:
                logger.warning(f"Could not store embeddings: {e}")
        
        # Return results
        return {
            "video_id": video_id,
            "keyframes_processed": len(processed_keyframes),
            "keyframe_percentage": settings.SCENE_KEYFRAME_PERCENTAGE * 100,
            "processing_method": "scene_detection_clip_optical_flow",
            "keyframes": [
                {
                    "timestamp": kf.timestamp,
                    "scene_id": kf.scene_id,
                    "scene_change_score": kf.scene_change_score,
                    "transition_type": kf.transition_type,
                    "has_clip_embedding": kf.embedding.clip_embedding is not None,
                    "has_optical_flow": kf.embedding.optical_flow_features is not None,
                    "frame_path": kf.frame_path
                }
                for kf in processed_keyframes[:20]  # Return first 20 for preview
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in scene keyframe analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{video_id}/thumbnails")
async def generate_video_thumbnails(
    video_id: int,
    interval: float = 10.0,
    db: Session = Depends(get_db)
):
    """Generate real thumbnails for video timeline"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")

        if not video.file_path or not os.path.exists(video.file_path):
            raise HTTPException(status_code=400, detail="Video file not found")

        # Initialize thumbnail service
        thumbnail_service = ThumbnailService()

        # Generate timeline thumbnails
        thumbnails = thumbnail_service.generate_timeline_thumbnails(
            video.file_path,
            interval=interval
        )

        return {
            "video_id": video_id,
            "thumbnails": thumbnails,
            "interval_seconds": interval,
            "total_thumbnails": len(thumbnails)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating thumbnails: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Native Video Search Endpoints using Gemini 2.5

class NativeSearchRequest(BaseModel):
    video_id: int
    query: str
    search_type: str = "general"  # general, object, counting, color, text, scene

class NativeSearchResponse(BaseModel):
    query: str
    search_type: str
    clips: List[Dict]
    total_clips: int
    processing_time: float
    
class CountingRequest(BaseModel):
    video_id: int
    element: str
    count_type: str = "unique"  # unique or total

class CountingResponse(BaseModel):
    element: str
    count_type: str
    total_count: int
    instances: List[Dict]
    temporal_pattern: str
    clips: List[Dict]
    confidence: float

class ColorObjectRequest(BaseModel):
    video_id: int
    color: str
    object_type: str

@router.post("/native/search", response_model=NativeSearchResponse)
async def native_video_search(
    search_request: NativeSearchRequest,
    db: Session = Depends(get_db)
):
    """Native video search using Gemini 2.5's video understanding capabilities"""
    try:
        import time
        start_time = time.time()
        
        # Get video
        video = db.query(Video).filter(Video.id == search_request.video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path or not os.path.exists(video.file_path):
            raise HTTPException(status_code=400, detail="Video file not found")
        
        # Initialize native search service
        search_service = NativeVideoSearchService()
        
        # Perform search based on type
        clips = await search_service.search_visual_content(
            video_path=video.file_path,
            query=search_request.query,
            search_type=search_request.search_type
        )
        
        # Convert clips to dict format
        clips_data = [clip.to_dict() for clip in clips]
        
        processing_time = time.time() - start_time
        
        return NativeSearchResponse(
            query=search_request.query,
            search_type=search_request.search_type,
            clips=clips_data,
            total_clips=len(clips_data),
            processing_time=round(processing_time, 2)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in native video search: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/native/count", response_model=CountingResponse)
async def native_count_elements(
    counting_request: CountingRequest,
    db: Session = Depends(get_db)
):
    """Count visual elements in video using native video understanding"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == counting_request.video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path or not os.path.exists(video.file_path):
            raise HTTPException(status_code=400, detail="Video file not found")
        
        # Perform counting
        result = await count_occurrences(
            video.file_path,
            counting_request.element,
            unique_only=(counting_request.count_type == "unique")
        )
        
        return CountingResponse(
            element=counting_request.element,
            count_type=counting_request.count_type,
            total_count=result.get("total_count", 0),
            instances=result.get("instances", []),
            temporal_pattern=result.get("temporal_pattern", ""),
            clips=result.get("clips", []),
            confidence=result.get("confidence", 0.8)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in native counting: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/native/color-object")
async def native_color_object_search(
    color_request: ColorObjectRequest,
    db: Session = Depends(get_db)
):
    """Search for specific color + object combinations"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == color_request.video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path or not os.path.exists(video.file_path):
            raise HTTPException(status_code=400, detail="Video file not found")
        
        # Search for color + object
        clips = await find_color_objects(
            video.file_path,
            color_request.color,
            color_request.object_type
        )
        
        return {
            "query": f"{color_request.color} {color_request.object_type}",
            "clips": clips,
            "total_clips": len(clips),
            "search_type": "color_object_combo"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in color-object search: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/native/text-search")
async def native_text_search(
    video_id: int,
    text: str,
    db: Session = Depends(get_db)
):
    """Search for text appearing in video"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path or not os.path.exists(video.file_path):
            raise HTTPException(status_code=400, detail="Video file not found")
        
        # Search for text
        clips = await find_text(video.file_path, text)
        
        return {
            "query": text,
            "clips": clips,
            "total_clips": len(clips),
            "search_type": "text_detection"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in text search: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/native/scene-search")
async def native_scene_search(
    video_id: int,
    scene_type: str,
    db: Session = Depends(get_db)
):
    """Search for specific types of scenes"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path or not os.path.exists(video.file_path):
            raise HTTPException(status_code=400, detail="Video file not found")
        
        # Search for scenes
        clips = await find_scenes(video.file_path, scene_type)
        
        return {
            "query": scene_type,
            "clips": clips,
            "total_clips": len(clips),
            "search_type": "scene_analysis"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in scene search: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/native/find-all")
async def native_find_all_occurrences(
    video_id: int,
    element: str,
    db: Session = Depends(get_db)
):
    """Find all occurrences of a visual element throughout the video"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path or not os.path.exists(video.file_path):
            raise HTTPException(status_code=400, detail="Video file not found")
        
        # Initialize service and find all occurrences
        search_service = NativeVideoSearchService()
        clips = await search_service.find_all_occurrences(video.file_path, element)
        
        # Convert to dict format
        clips_data = [clip.to_dict() for clip in clips]
        
        return {
            "element": element,
            "clips": clips_data,
            "total_occurrences": len(clips_data),
            "search_type": "exhaustive_search"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error finding all occurrences: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/native/cleanup/{video_id}")
async def cleanup_native_upload(
    video_id: int,
    db: Session = Depends(get_db)
):
    """Clean up uploaded video from Gemini to free resources"""
    try:
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path:
            return {"message": "No video file to clean up"}
        
        # Initialize service and cleanup
        search_service = NativeVideoSearchService()
        success = await search_service.cleanup_upload(video.file_path)
        
        return {
            "video_id": video_id,
            "cleanup_success": success,
            "message": "Video upload cleaned up from Gemini" if success else "No upload found to clean"
        }
        
    except Exception as e:
        logger.error(f"Error cleaning up video upload: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{video_id}/search-suggestions")
async def get_search_suggestions(
    video_id: int,
    db: Session = Depends(get_db)
):
    """Get dynamic search suggestions based on video content"""
    try:
        # Get video and frames
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        frames = db.query(VideoFrame)\
            .filter(VideoFrame.video_id == video_id)\
            .filter(VideoFrame.description.isnot(None))\
            .all()
        
        if not frames:
            return {
                "video_id": video_id,
                "suggestions": ["Please analyze video frames first"],
                "content_summary": {}
            }
        
        # Analyze content dynamically
        from app.services.dynamic_content_analyzer import DynamicContentAnalyzer
        analyzer = DynamicContentAnalyzer()
        
        frame_data = [
            {'description': f.description, 'timestamp': f.timestamp} 
            for f in frames if f.description
        ]
        
        video_content = analyzer.analyze_video_content(frame_data)
        
        # Return suggestions and content summary
        return {
            "video_id": video_id,
            "suggestions": video_content.suggested_queries,
            "content_summary": {
                "top_objects": list(video_content.objects.keys())[:10],
                "top_colors": list(video_content.colors.keys())[:5],
                "top_actions": list(video_content.actions.keys())[:5],
                "detected_text": video_content.text_elements[:5],
                "scene_types": list(video_content.scenes.keys())[:3]
            },
            "total_frames_analyzed": len(frames)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating search suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{video_id}/frame")
async def get_video_frame_at_timestamp(
    video_id: int,
    timestamp: float,
    size: str = "medium",
    db: Session = Depends(get_db)
):
    """Get a frame from the video at a specific timestamp"""
    try:
        from fastapi.responses import FileResponse
        import cv2
        import tempfile
        
        # Get video
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise HTTPException(status_code=404, detail="Video not found")
        
        if not video.file_path or not os.path.exists(video.file_path):
            # Return a placeholder image if video file not found
            raise HTTPException(status_code=404, detail="Video file not found")
        
        # Initialize thumbnail service if available
        thumbnail_service = None
        try:
            thumbnail_service = ThumbnailService()
        except Exception as e:
            logger.warning(f"Could not initialize thumbnail service: {e}")
        
        # Try to use thumbnail service first
        if thumbnail_service:
            try:
                thumbnail_path = thumbnail_service.get_frame_at_timestamp(
                    video.file_path,
                    timestamp,
                    size=size
                )
                if thumbnail_path and os.path.exists(thumbnail_path):
                    return FileResponse(
                        thumbnail_path,
                        media_type="image/jpeg",
                        headers={"Cache-Control": "public, max-age=3600"}
                    )
            except Exception as e:
                logger.warning(f"Thumbnail service failed: {e}")
        
        # Fallback to direct frame extraction
        cap = cv2.VideoCapture(video.file_path)
        try:
            # Set position to timestamp
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_number = int(timestamp * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # Read frame
            ret, frame = cap.read()
            if not ret:
                raise HTTPException(status_code=404, detail="Could not extract frame")
            
            # Resize based on size parameter
            sizes = {
                "small": (120, 68),
                "medium": (240, 135),
                "large": (480, 270),
                "timeline": (160, 90)
            }
            
            target_size = sizes.get(size, sizes["medium"])
            frame = cv2.resize(frame, target_size)
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp:
                cv2.imwrite(tmp.name, frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                return FileResponse(
                    tmp.name,
                    media_type="image/jpeg",
                    headers={"Cache-Control": "public, max-age=3600"}
                )
                
        finally:
            cap.release()
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting frame: {e}")
        raise HTTPException(status_code=500, detail=str(e))
