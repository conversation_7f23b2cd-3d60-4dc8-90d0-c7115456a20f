"""
Enhanced Vector Service with Direct Image Embeddings and Hybrid Search
Implements the suggested improvements for production-ready multimodal search
"""

import logging
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from PIL import Image
import chromadb
from chromadb.config import Settings
import json
import re
from dataclasses import dataclass
import os

# Graceful imports for optional dependencies
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None

try:
    import open_clip
    CLIP_AVAILABLE = True
except ImportError:
    try:
        import clip
        CLIP_AVAILABLE = True
        open_clip = None
    except ImportError:
        CLIP_AVAILABLE = False
        clip = None
        open_clip = None

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    SentenceTransformer = None

try:
    from transformers import CLIPProcessor, CLIPModel
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    CLIPProcessor = None
    CLIPModel = None

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Enhanced search result with multiple embedding types"""
    frame_id: str
    timestamp: float
    confidence: float
    description: str
    frame_path: str
    image_similarity: float
    text_similarity: float
    keyword_matches: List[str]
    metadata: Dict[str, Any]

class EnhancedVectorService:
    """Enhanced vector service with direct image embeddings and hybrid search"""

    def __init__(self):
        self.client = None
        self.frames_collection = None
        self.available = False

        # Multiple embedding models
        self.clip_model = None
        self.clip_preprocess = None
        self.text_model = None
        self.device = "cpu"

        # Model availability flags
        self.has_clip = False
        self.has_text_model = False
        self.has_torch = TORCH_AVAILABLE

        # Hybrid search components
        self.keyword_index = {}  # Simple keyword index

        self._initialize_services()

    def _initialize_services(self):
        """Initialize ChromaDB and embedding models with graceful fallbacks"""
        try:
            # Initialize ChromaDB
            self.client = chromadb.PersistentClient(
                path="./chroma_db",
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )

            # Create enhanced collection with metadata
            self.frames_collection = self.client.get_or_create_collection(
                name="enhanced_video_frames",
                metadata={"hnsw:space": "cosine"}
            )

            # Set device for PyTorch models
            if TORCH_AVAILABLE:
                self.device = "cuda" if torch.cuda.is_available() else "cpu"
                logger.info(f"PyTorch available, using device: {self.device}")
            else:
                logger.warning("PyTorch not available, using CPU-only fallbacks")

            # Initialize CLIP for direct image embeddings
            self._initialize_clip_model()

            # Initialize text embedding model
            self._initialize_text_model()

            # Service is available if we have at least ChromaDB
            self.available = True

            # Log capabilities
            capabilities = []
            if self.has_clip:
                capabilities.append("CLIP image embeddings")
            if self.has_text_model:
                capabilities.append("sentence transformers")
            if not capabilities:
                capabilities.append("basic text embeddings")

            logger.info(f"Enhanced Vector Service initialized with: {', '.join(capabilities)}")

        except Exception as e:
            logger.error(f"Failed to initialize Enhanced Vector Service: {e}")
            self.available = False

    def _initialize_clip_model(self):
        """Initialize CLIP model with multiple fallback options"""
        if not TORCH_AVAILABLE:
            logger.warning("PyTorch not available, skipping CLIP initialization")
            return

        # Try OpenCLIP first (more stable)
        if open_clip and CLIP_AVAILABLE:
            try:
                self.clip_model, _, self.clip_preprocess = open_clip.create_model_and_transforms(
                    'ViT-B-32',
                    pretrained='openai',
                    device=self.device
                )
                self.has_clip = True
                logger.info(f"OpenCLIP model loaded on {self.device}")
                return
            except Exception as e:
                logger.warning(f"Failed to load OpenCLIP model: {e}")

        # Try original CLIP as fallback
        if clip and CLIP_AVAILABLE:
            try:
                self.clip_model, self.clip_preprocess = clip.load("ViT-B/32", device=self.device)
                self.has_clip = True
                logger.info(f"Original CLIP model loaded on {self.device}")
                return
            except Exception as e:
                logger.warning(f"Failed to load original CLIP model: {e}")

        # Try Transformers CLIP as final fallback
        if TRANSFORMERS_AVAILABLE:
            try:
                from transformers import CLIPProcessor, CLIPModel
                self.clip_model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
                self.clip_preprocess = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
                if TORCH_AVAILABLE:
                    self.clip_model = self.clip_model.to(self.device)
                self.has_clip = True
                logger.info(f"Transformers CLIP model loaded on {self.device}")
                return
            except Exception as e:
                logger.warning(f"Failed to load Transformers CLIP model: {e}")

        logger.warning("No CLIP model could be loaded, image embeddings will not be available")

    def _initialize_text_model(self):
        """Initialize text embedding model with fallbacks"""
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                self.text_model = SentenceTransformer('all-MiniLM-L6-v2')
                self.has_text_model = True
                logger.info("SentenceTransformer model loaded")
                return
            except Exception as e:
                logger.warning(f"Failed to load SentenceTransformer: {e}")

        logger.warning("SentenceTransformers not available, using basic text embeddings")
    
    def _generate_image_embedding(self, image_path: str) -> Optional[List[float]]:
        """Generate direct image embedding using CLIP with multiple model support"""
        if not self.has_clip or not self.clip_model:
            return None

        try:
            image = Image.open(image_path).convert('RGB')

            # Handle different CLIP model types
            if hasattr(self.clip_model, 'encode_image'):
                # Original CLIP or OpenCLIP
                if TORCH_AVAILABLE:
                    image_input = self.clip_preprocess(image).unsqueeze(0).to(self.device)
                    with torch.no_grad():
                        image_features = self.clip_model.encode_image(image_input)
                        # Normalize features
                        image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                    return image_features.cpu().numpy().flatten().tolist()
                else:
                    logger.warning("PyTorch not available for image processing")
                    return None

            elif hasattr(self.clip_model, 'get_image_features'):
                # Transformers CLIP
                if TORCH_AVAILABLE:
                    inputs = self.clip_preprocess(images=image, return_tensors="pt")
                    if self.device != "cpu":
                        inputs = {k: v.to(self.device) for k, v in inputs.items()}

                    with torch.no_grad():
                        image_features = self.clip_model.get_image_features(**inputs)
                        # Normalize features
                        image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                    return image_features.cpu().numpy().flatten().tolist()
                else:
                    logger.warning("PyTorch not available for image processing")
                    return None
            else:
                logger.error("Unknown CLIP model type")
                return None

        except Exception as e:
            logger.error(f"Error generating image embedding: {e}")
            return None
    
    def _generate_text_embedding(self, text: str) -> List[float]:
        """Generate text embedding with multiple model support"""
        if self.has_text_model and self.text_model:
            try:
                return self.text_model.encode(text).tolist()
            except Exception as e:
                logger.warning(f"Failed to generate text embedding with SentenceTransformer: {e}")

        # Fallback to simple hash-based embedding
        import hashlib
        hash_obj = hashlib.md5(text.encode())
        hash_hex = hash_obj.hexdigest()
        embedding = [float(int(hash_hex[i:i+2], 16)) / 255.0 for i in range(0, min(32, len(hash_hex)), 2)]
        while len(embedding) < 16:
            embedding.append(0.0)
        return embedding[:16]

    def _generate_clip_text_embedding(self, text: str) -> Optional[List[float]]:
        """Generate text embedding using CLIP text encoder for visual queries"""
        if not self.has_clip or not self.clip_model:
            return None

        try:
            # Handle different CLIP model types
            if hasattr(self.clip_model, 'encode_text'):
                # Original CLIP or OpenCLIP
                if TORCH_AVAILABLE:
                    # Try OpenCLIP tokenizer first
                    if open_clip:
                        text_input = open_clip.tokenize([text]).to(self.device)
                    elif clip:
                        text_input = clip.tokenize([text]).to(self.device)
                    else:
                        logger.warning("No tokenizer available for CLIP text embedding")
                        return None

                    with torch.no_grad():
                        text_features = self.clip_model.encode_text(text_input)
                        text_features = text_features / text_features.norm(dim=-1, keepdim=True)
                    return text_features.cpu().numpy().flatten().tolist()

            elif hasattr(self.clip_model, 'get_text_features'):
                # Transformers CLIP
                if TORCH_AVAILABLE:
                    inputs = self.clip_preprocess(text=[text], return_tensors="pt", padding=True)
                    if self.device != "cpu":
                        inputs = {k: v.to(self.device) for k, v in inputs.items()}

                    with torch.no_grad():
                        text_features = self.clip_model.get_text_features(**inputs)
                        text_features = text_features / text_features.norm(dim=-1, keepdim=True)
                    return text_features.cpu().numpy().flatten().tolist()

        except Exception as e:
            logger.error(f"Error generating CLIP text embedding: {e}")

        return None
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text for hybrid search"""
        # Simple keyword extraction
        text = text.lower()
        # Remove common stop words and extract meaningful terms
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'}
        
        # Extract words (alphanumeric sequences)
        words = re.findall(r'\b[a-zA-Z0-9]+\b', text)
        keywords = [word for word in words if len(word) > 2 and word not in stop_words]
        
        return list(set(keywords))  # Remove duplicates
    
    def _build_keyword_index(self, frame_id: str, description: str):
        """Build keyword index for hybrid search"""
        keywords = self._extract_keywords(description)
        
        for keyword in keywords:
            if keyword not in self.keyword_index:
                self.keyword_index[keyword] = []
            self.keyword_index[keyword].append(frame_id)
    
    async def add_enhanced_frame_embedding(self,
                                         frame_id: str,
                                         description: str,
                                         image_path: str,
                                         metadata: Dict) -> bool:
        """Add frame with both image and text embeddings"""
        try:
            # Generate both image and text embeddings
            image_embedding = self._generate_image_embedding(image_path)
            text_embedding = self._generate_text_embedding(description)
            
            # Combine embeddings (concatenate or use primary embedding)
            if image_embedding and text_embedding:
                # Use image embedding as primary, store text embedding in metadata
                primary_embedding = image_embedding
                enhanced_metadata = metadata.copy()
                enhanced_metadata['text_embedding'] = text_embedding
                enhanced_metadata['has_image_embedding'] = True
            elif text_embedding:
                # Fallback to text embedding only
                primary_embedding = text_embedding
                enhanced_metadata = metadata.copy()
                enhanced_metadata['has_image_embedding'] = False
            else:
                logger.error(f"Failed to generate any embedding for frame {frame_id}")
                return False
            
            # Store keywords for hybrid search
            enhanced_metadata['keywords'] = self._extract_keywords(description)
            
            # Add to ChromaDB
            self.frames_collection.add(
                embeddings=[primary_embedding],
                documents=[description],
                metadatas=[enhanced_metadata],
                ids=[frame_id]
            )
            
            # Build keyword index
            self._build_keyword_index(frame_id, description)
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding enhanced frame embedding: {e}")
            return False
    
    async def hybrid_search(self,
                          query: str,
                          video_id: Optional[int] = None,
                          limit: int = 10,
                          keyword_weight: float = 0.3,
                          vector_weight: float = 0.7,
                          use_visual_search: bool = True) -> List[SearchResult]:
        """Perform hybrid search combining keyword and vector search"""
        try:
            # Step 1: Keyword pre-filtering
            query_keywords = self._extract_keywords(query)
            keyword_candidates = set()
            
            for keyword in query_keywords:
                if keyword in self.keyword_index:
                    keyword_candidates.update(self.keyword_index[keyword])
            
            # Step 2: Generate query embedding (visual or text based on query type)
            query_embedding = None

            # Detect if this is a visual query
            visual_indicators = ['red', 'blue', 'green', 'yellow', 'black', 'white', 'color',
                               'bright', 'dark', 'large', 'small', 'round', 'square',
                               'person', 'people', 'car', 'object', 'thing', 'item']

            is_visual_query = any(word in query.lower() for word in visual_indicators)

            if use_visual_search and is_visual_query and self.has_clip:
                # For visual queries, use CLIP text encoder to match against image embeddings
                query_embedding = self._generate_clip_text_embedding(query)
                if query_embedding:
                    logger.info(f"Using CLIP visual search for query: {query}")
                else:
                    logger.warning("Failed to use CLIP for query, falling back to text")
                    query_embedding = self._generate_text_embedding(query)
            else:
                # Use text embedding for non-visual queries
                query_embedding = self._generate_text_embedding(query)
            
            # Prepare where clause for video filtering
            where_clause = {}
            if video_id:
                where_clause['video_id'] = video_id
            
            # Perform vector search
            vector_results = self.frames_collection.query(
                query_embeddings=[query_embedding],
                n_results=limit * 2,  # Get more results for hybrid ranking
                where=where_clause if where_clause else None,
                include=['embeddings', 'documents', 'metadatas', 'distances']
            )
            
            # Step 3: Hybrid scoring and ranking
            enhanced_results = []
            
            for i, (frame_id, document, metadata, distance) in enumerate(zip(
                vector_results['ids'][0],
                vector_results['documents'][0],
                vector_results['metadatas'][0],
                vector_results['distances'][0]
            )):
                # Calculate vector similarity (convert distance to similarity)
                vector_similarity = max(0, 1 - distance)
                
                # Calculate keyword similarity
                frame_keywords = set(metadata.get('keywords', []))
                query_keywords_set = set(query_keywords)
                
                if query_keywords_set and frame_keywords:
                    keyword_similarity = len(query_keywords_set.intersection(frame_keywords)) / len(query_keywords_set.union(frame_keywords))
                else:
                    keyword_similarity = 0
                
                # Calculate hybrid score with conservative weighting
                hybrid_score = (vector_weight * vector_similarity) + (keyword_weight * keyword_similarity)
                
                # Small bonus for keyword pre-filtering matches (reduced from 0.1 to 0.05)
                if frame_id in keyword_candidates and keyword_similarity > 0:
                    hybrid_score += 0.05
                
                # Apply exponential decay for more realistic confidence distribution
                if hybrid_score > 0.8:
                    hybrid_score = 0.8 + (hybrid_score - 0.8) * 0.5  # Compress high scores
                elif hybrid_score < 0.2:
                    hybrid_score = hybrid_score * 0.5  # Further reduce low scores
                
                enhanced_results.append(SearchResult(
                    frame_id=frame_id,
                    timestamp=metadata.get('timestamp', 0),
                    confidence=min(100, hybrid_score * 100),
                    description=document,
                    frame_path=metadata.get('frame_path', ''),
                    image_similarity=vector_similarity if metadata.get('has_image_embedding') else 0,
                    text_similarity=vector_similarity,
                    keyword_matches=list(query_keywords_set.intersection(frame_keywords)),
                    metadata=metadata
                ))
            
            # Sort by hybrid score and return top results
            enhanced_results.sort(key=lambda x: x.confidence, reverse=True)
            return enhanced_results[:limit]
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics with enhanced capability reporting"""
        try:
            collection_count = self.frames_collection.count() if self.frames_collection else 0
            keyword_count = len(self.keyword_index)

            # Determine CLIP model type
            clip_model_type = "none"
            if self.has_clip and self.clip_model:
                if hasattr(self.clip_model, 'encode_image') and hasattr(self.clip_model, 'encode_text'):
                    if open_clip:
                        clip_model_type = "openclip"
                    else:
                        clip_model_type = "original_clip"
                elif hasattr(self.clip_model, 'get_image_features'):
                    clip_model_type = "transformers_clip"

            return {
                "available": self.available,
                "total_frames": collection_count,
                "keyword_index_size": keyword_count,
                "capabilities": {
                    "pytorch_available": TORCH_AVAILABLE,
                    "clip_available": CLIP_AVAILABLE,
                    "sentence_transformers_available": SENTENCE_TRANSFORMERS_AVAILABLE,
                    "transformers_available": TRANSFORMERS_AVAILABLE,
                    "has_clip_model": self.has_clip,
                    "has_text_model": self.has_text_model,
                    "clip_model_type": clip_model_type,
                    "device": self.device
                },
                "models_loaded": {
                    "clip": self.clip_model is not None,
                    "text_embeddings": self.text_model is not None
                }
            }
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {"available": False, "error": str(e)}
