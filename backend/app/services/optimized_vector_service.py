"""
Optimized Vector Service using Fast Hybrid Search
- Single embedding model (SentenceTransformers)
- BM25 + Dense hybrid search
- FAISS for ultra-fast vector search
- Singleton pattern for shared state
"""

import logging
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

from .fast_hybrid_search import FastHybridSearchService

logger = logging.getLogger(__name__)

@dataclass
class OptimizedSearchResult:
    timestamp: float
    confidence: float
    description: str
    frame_path: str
    image_similarity: float = 0.0
    text_similarity: float = 0.0
    keyword_matches: List[str] = None
    metadata: Dict[str, Any] = None

class OptimizedVectorService:
    """
    Optimized vector service using fast hybrid search
    - Single embedding model (SentenceTransformers)
    - BM25 + Dense hybrid search
    - FAISS for ultra-fast vector search
    - No dual embedding complexity
    - Singleton pattern for shared state
    """

    _instance = None
    _search_engine = None

    def __new__(cls):
        """Singleton pattern to ensure shared state"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize optimized vector service (only once)"""
        if self._initialized:
            return
            
        logger.info("🚀 Initializing Optimized Vector Service...")

        # Use shared fast hybrid search engine
        if OptimizedVectorService._search_engine is None:
            OptimizedVectorService._search_engine = FastHybridSearchService()

        self.search_engine = OptimizedVectorService._search_engine
        self.available = self.search_engine.available
        self._initialized = True

        if self.available:
            logger.info("✅ Optimized Vector Service ready!")
        else:
            logger.error("❌ Failed to initialize Optimized Vector Service")
    
    async def add_frame_embedding(self,
                                frame_id: str,
                                description: str,
                                timestamp: float,
                                frame_path: str,
                                video_id: int,
                                metadata: Dict[str, Any] = None) -> bool:
        """Add a single frame embedding"""
        if not self.available:
            return False
        
        try:
            return await self.search_engine.add_frame(
                frame_id=frame_id,
                description=description,
                timestamp=timestamp,
                frame_path=frame_path,
                video_id=video_id,
                metadata=metadata or {}
            )
        except Exception as e:
            logger.error(f"Failed to add frame embedding: {e}")
            return False

    async def batch_add_frames(self, frames_data: List[Dict[str, Any]]) -> int:
        """Batch add multiple frames"""
        if not self.available:
            return 0
        
        try:
            return await self.search_engine.batch_add_frames(frames_data)
        except Exception as e:
            logger.error(f"Failed to batch add frames: {e}")
            return 0

    async def search_visual(self,
                          query: str,
                          video_id: Optional[int] = None,
                          limit: int = 10) -> List[OptimizedSearchResult]:
        """Search for visual content using hybrid approach"""
        if not self.available:
            return []
        
        try:
            results = await self.search_engine.search(
                query=query,
                video_id=video_id,
                limit=limit
            )
            
            # Convert to OptimizedSearchResult format
            optimized_results = []
            for result in results:
                optimized_results.append(OptimizedSearchResult(
                    timestamp=result.timestamp,
                    confidence=result.confidence,
                    description=result.description,
                    frame_path=result.frame_path,
                    image_similarity=result.dense_score,
                    text_similarity=result.bm25_score,
                    keyword_matches=result.keyword_matches,
                    metadata=result.metadata
                ))
            
            return optimized_results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []

    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics"""
        if not self.available:
            return {"available": False}
        
        try:
            return self.search_engine.get_stats()
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {"available": False, "error": str(e)}

    def clear_index(self) -> bool:
        """Clear the search index"""
        if not self.available:
            return False
        
        try:
            return self.search_engine.clear_index()
        except Exception as e:
            logger.error(f"Failed to clear index: {e}")
            return False
