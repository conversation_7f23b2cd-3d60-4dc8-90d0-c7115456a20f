"""
Optimized Vector Service using Fast Hybrid Search
Replaces the complex dual embedding approach with a single, fast, accurate solution
"""

import logging
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
import asyncio

from .fast_hybrid_search import FastHybridSearchService, FastSearchResult

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Compatibility wrapper for existing SearchResult interface"""
    frame_id: str
    timestamp: float
    confidence: float
    description: str
    frame_path: str
    image_similarity: float = 0.0
    text_similarity: float = 0.0
    keyword_matches: List[str] = None
    metadata: Dict[str, Any] = None

class OptimizedVectorService:
    """
    Optimized vector service using fast hybrid search
    - Single embedding model (SentenceTransformers)
    - BM25 + Dense hybrid search
    - FAISS for ultra-fast vector search
    - No dual embedding complexity
    - Singleton pattern for shared state
    """

    _instance = None
    _search_engine = None

    def __new__(cls):
        """Singleton pattern to ensure shared state"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize optimized vector service (only once)"""
        if self._initialized:
            return

        logger.info("🚀 Initializing Optimized Vector Service...")

        # Use shared fast hybrid search engine
        if OptimizedVectorService._search_engine is None:
            OptimizedVectorService._search_engine = FastHybridSearchService()

        self.search_engine = OptimizedVectorService._search_engine
        self.available = self.search_engine.available
        self._initialized = True

        if self.available:
            logger.info("✅ Optimized Vector Service ready!")
        else:
            logger.error("❌ Failed to initialize Optimized Vector Service")
    
    async def add_frame_embedding(self,
                                frame_id: str,
                                description: str,
                                timestamp: float,
                                frame_path: str,
                                video_id: int,
                                metadata: Dict[str, Any] = None) -> bool:
        """Add frame embedding to the search index"""
        if not self.available:
            return False
        
        return await self.search_engine.add_frame(
            frame_id=frame_id,
            description=description,
            timestamp=timestamp,
            frame_path=frame_path,
            video_id=video_id,
            metadata=metadata
        )
    
    async def search_visual(self,
                          query: str,
                          video_id: Optional[int] = None,
                          limit: int = 10) -> List[SearchResult]:
        """
        Perform visual search using hybrid approach
        
        Args:
            query: Search query
            video_id: Optional video ID filter
            limit: Maximum results to return
        """
        if not self.available:
            return []
        
        # Use fast hybrid search
        fast_results = await self.search_engine.search(
            query=query,
            video_id=video_id,
            limit=limit,
            bm25_weight=0.3,  # Keyword matching
            dense_weight=0.7   # Semantic similarity
        )
        
        # Convert to compatible SearchResult format
        results = []
        for fast_result in fast_results:
            result = SearchResult(
                frame_id=fast_result.frame_id,
                timestamp=fast_result.timestamp,
                confidence=fast_result.confidence,
                description=fast_result.description,
                frame_path=fast_result.frame_path,
                image_similarity=0.0,  # Not used in optimized version
                text_similarity=fast_result.dense_score * 100,
                keyword_matches=fast_result.keyword_matches,
                metadata=fast_result.metadata
            )
            results.append(result)
        
        return results
    
    async def search_semantic(self,
                            query: str,
                            video_id: Optional[int] = None,
                            limit: int = 10) -> List[SearchResult]:
        """
        Semantic search (same as visual search in optimized version)
        """
        return await self.search_visual(query, video_id, limit)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics"""
        if not self.available:
            return {"available": False}
        
        base_stats = self.search_engine.get_stats()
        
        # Add compatibility fields
        return {
            **base_stats,
            "enhanced_features_available": True,
            "processing_method": "fast_hybrid_search",
            "capabilities": {
                **base_stats.get("capabilities", {}),
                "hybrid_search": True,
                "bm25_keyword_search": True,
                "faiss_vector_search": True,
                "single_embedding_model": True
            }
        }
    
    async def batch_add_frames(self, frames_data: List[Dict[str, Any]]) -> int:
        """
        Batch add multiple frames for better performance
        
        Args:
            frames_data: List of frame dictionaries with keys:
                - frame_id, description, timestamp, frame_path, video_id, metadata
        
        Returns:
            Number of successfully added frames
        """
        if not self.available:
            return 0
        
        success_count = 0
        for frame_data in frames_data:
            try:
                success = await self.add_frame_embedding(
                    frame_id=frame_data["frame_id"],
                    description=frame_data["description"],
                    timestamp=frame_data["timestamp"],
                    frame_path=frame_data["frame_path"],
                    video_id=frame_data["video_id"],
                    metadata=frame_data.get("metadata", {})
                )
                if success:
                    success_count += 1
            except Exception as e:
                logger.error(f"Error adding frame {frame_data.get('frame_id', 'unknown')}: {e}")
        
        # Rebuild indices for optimal performance after batch insert
        if success_count > 0:
            await self.search_engine.rebuild_indices()
            logger.info(f"✅ Batch added {success_count}/{len(frames_data)} frames")
        
        return success_count
    
    async def optimize_indices(self):
        """Optimize search indices for better performance"""
        if self.available:
            await self.search_engine.rebuild_indices()
            logger.info("🔧 Search indices optimized")
    
    def is_enhanced_available(self) -> bool:
        """Check if enhanced features are available"""
        return self.available
    
    def get_embedding_dimension(self) -> int:
        """Get embedding dimension"""
        return 384  # SentenceTransformers all-MiniLM-L6-v2
    
    async def clear_video_embeddings(self, video_id: int):
        """Clear embeddings for a specific video (for reprocessing)"""
        # Note: This would require implementing in FastHybridSearchService
        # For now, log the request
        logger.info(f"Clear embeddings requested for video {video_id} (not implemented)")
    
    def get_processing_method(self) -> str:
        """Get the current processing method"""
        return "fast_hybrid_search_optimized"

# Create global instance for backward compatibility
optimized_vector_service = OptimizedVectorService()

# Alias for easy import
VectorService = OptimizedVectorService
