"""
Dynamic Content Analyzer - Extracts searchable content from any video
No hardcoded mappings - works with any video content
"""

import re
from typing import List, Dict, Set, Tuple, Optional
from collections import Counter, defaultdict
import logging
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class VideoContent:
    """Represents analyzed video content"""
    objects: Dict[str, int]  # object -> count
    colors: Dict[str, int]  # color -> count
    actions: Dict[str, int]  # action -> count
    scenes: Dict[str, int]  # scene type -> count
    text_elements: List[str]  # detected text
    temporal_patterns: Dict[str, List[float]]  # pattern -> timestamps
    suggested_queries: List[str]  # AI-generated suggestions

class DynamicContentAnalyzer:
    """Analyzes video content dynamically without hardcoded mappings"""
    
    def __init__(self):
        # Basic patterns for extraction (not content-specific)
        self.color_pattern = re.compile(r'\b((?:light|dark|bright|pale|deep|vivid|)\s*(?:\w+))\s+(?:color|colored|colour)\b', re.IGNORECASE)
        self.object_pattern = re.compile(r'\b(?:a|an|the|some|many|several)\s+(\w+(?:\s+\w+)?)\b', re.IGNORECASE)
        self.action_pattern = re.compile(r'\b(\w+ing)\b', re.IGNORECASE)
        
    def analyze_video_content(self, frame_descriptions: List[Dict]) -> VideoContent:
        """Dynamically analyze video content from frame descriptions"""
        # Initialize counters
        all_objects = Counter()
        all_colors = Counter()
        all_actions = Counter()
        all_scenes = Counter()
        all_text = []
        temporal_patterns = defaultdict(list)
        
        # Process each frame
        for frame in frame_descriptions:
            if not frame.get('description'):
                continue
                
            description = frame['description']
            timestamp = frame.get('timestamp', 0)
            
            # Extract objects dynamically
            objects = self._extract_objects(description)
            for obj in objects:
                all_objects[obj] += 1
                temporal_patterns[obj].append(timestamp)
            
            # Extract colors dynamically
            colors = self._extract_colors(description)
            for color in colors:
                all_colors[color] += 1
                temporal_patterns[f"color:{color}"].append(timestamp)
            
            # Extract actions dynamically
            actions = self._extract_actions(description)
            for action in actions:
                all_actions[action] += 1
                temporal_patterns[f"action:{action}"].append(timestamp)
            
            # Extract scene types dynamically
            scene_type = self._identify_scene_type(description)
            if scene_type:
                all_scenes[scene_type] += 1
            
            # Extract text mentions
            text_elements = self._extract_text_mentions(description)
            all_text.extend(text_elements)
        
        # Generate search suggestions based on actual content
        suggested_queries = self._generate_search_suggestions(
            all_objects, all_colors, all_actions, all_scenes
        )
        
        return VideoContent(
            objects=dict(all_objects.most_common(20)),  # Top 20 objects
            colors=dict(all_colors.most_common(10)),   # Top 10 colors
            actions=dict(all_actions.most_common(15)), # Top 15 actions
            scenes=dict(all_scenes.most_common(5)),    # Top 5 scene types
            text_elements=list(set(all_text))[:20],    # Unique text elements
            temporal_patterns=dict(temporal_patterns),
            suggested_queries=suggested_queries
        )
    
    def _extract_objects(self, description: str) -> List[str]:
        """Extract objects from description - find nouns that appear to be objects"""
        objects = []
        
        # Find nouns after determiners
        pattern = r'\b(?:a|an|the|some|many|several)\s+(\w+)\b'
        matches = re.findall(pattern, description, re.IGNORECASE)
        
        for match in matches:
            if len(match) > 2:  # Skip very short words
                objects.append(match.lower())
        
        return objects
    
    def _extract_colors(self, description: str) -> List[str]:
        """Extract words that appear to be colors"""
        colors = []
        
        # Find words that come before "color" or "colored"
        pattern = r'\b(\w+)\s+(?:color|colored|colour)\b'
        matches = re.findall(pattern, description, re.IGNORECASE)
        colors.extend([m.lower() for m in matches])
        
        # Find words ending in -ish (often colors)
        ish_pattern = r'\b(\w+ish)\b'
        ish_matches = re.findall(ish_pattern, description, re.IGNORECASE)
        colors.extend([m.lower() for m in ish_matches if len(m) > 4])
        
        return list(set(colors))
    
    def _extract_actions(self, description: str) -> List[str]:
        """Extract actions dynamically"""
        actions = []
        
        # Find -ing words that represent actions
        ing_words = re.findall(r'\b(\w+ing)\b', description, re.IGNORECASE)
        
        for word in ing_words:
            # Filter out non-action -ing words
            if word.lower() not in ['something', 'nothing', 'anything', 'everything', 'thing']:
                actions.append(word.lower())
        
        # Also find action phrases
        action_phrases = re.findall(r'\b(?:is|are|was|were)\s+(\w+ing)\b', description, re.IGNORECASE)
        actions.extend([phrase.lower() for phrase in action_phrases])
        
        return list(set(actions))
    
    def _identify_scene_type(self, description: str) -> Optional[str]:
        """Identify scene type from description"""
        # Look for location indicators
        location_match = re.search(r'\b(?:in|at|on)\s+(?:a|an|the)\s+(\w+)\b', description, re.IGNORECASE)
        if location_match:
            return f"scene:{location_match.group(1).lower()}"
        
        # Look for environment descriptions
        env_match = re.search(r'\b(\w+)\s+(?:environment|setting|scene|location)\b', description, re.IGNORECASE)
        if env_match:
            return f"environment:{env_match.group(1).lower()}"
        
        return None
    
    def _extract_text_mentions(self, description: str) -> List[str]:
        """Extract mentions of text in the video"""
        text_mentions = []
        
        # Look for text/sign mentions
        text_patterns = [
            r'(?:text|sign|writing|words?)\s+(?:says?|reads?|shows?)\s+"([^"]+)"',
            r'"([^"]+)"\s+(?:written|displayed|shown)',
            r'(?:displays?|shows?)\s+(?:the)?\s+(?:text|word|phrase)\s+"([^"]+)"'
        ]
        
        for pattern in text_patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            text_mentions.extend(matches)
        
        return text_mentions
    
    def _generate_search_suggestions(self, 
                                   objects: Counter, 
                                   colors: Counter, 
                                   actions: Counter,
                                   scenes: Counter) -> List[str]:
        """Generate search suggestions based on actual video content"""
        suggestions = []
        
        # Top objects
        top_objects = [obj for obj, count in objects.most_common(5) if count > 2]
        suggestions.extend(top_objects)
        
        # Color + object combinations
        if colors and objects:
            top_colors = [color for color, count in colors.most_common(3)]
            for color in top_colors:
                for obj in top_objects[:3]:
                    suggestions.append(f"{color} {obj}")
        
        # Action queries
        top_actions = [action for action, count in actions.most_common(3) if count > 2]
        for action in top_actions:
            # Combine with objects
            if top_objects:
                suggestions.append(f"{top_objects[0]} {action}")
            else:
                suggestions.append(action)
        
        # Scene-based queries
        for scene, count in scenes.most_common(2):
            if count > 3:
                suggestions.append(scene.replace('scene:', '').replace('environment:', ''))
        
        # Counting queries for frequent objects
        for obj, count in objects.most_common(3):
            if count > 5:
                suggestions.append(f"how many {obj}")
        
        # Remove duplicates and limit
        return list(dict.fromkeys(suggestions))[:15]
    
    def validate_query_against_content(self, 
                                     query: str, 
                                     video_content: VideoContent) -> Tuple[bool, float, str]:
        """Validate if a query makes sense for the video content"""
        query_lower = query.lower()
        query_words = query_lower.split()
        
        # Skip validation for very short queries
        if len(query_words) == 0:
            return True, 1.0, "Empty query"
            
        # For now, always return true to allow pure text matching
        # The actual validation happens during frame matching
        return True, 1.0, "Query accepted for matching"
    
    def get_temporal_distribution(self, 
                                element: str, 
                                video_content: VideoContent) -> List[float]:
        """Get timestamps where an element appears"""
        # Check various temporal patterns
        if element in video_content.temporal_patterns:
            return video_content.temporal_patterns[element]
        
        # Check with prefixes
        for key in video_content.temporal_patterns:
            if element in key or key.endswith(element):
                return video_content.temporal_patterns[key]
        
        return []