Defaulting to user installation because normal site-packages is not writeable
Collecting open-clip-torch
  Downloading open_clip_torch-2.32.0-py3-none-any.whl (1.5 MB)
Collecting ftfy
  Downloading ftfy-6.3.1-py3-none-any.whl (44 kB)
Requirement already satisfied: torchvision in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from open-clip-torch) (0.17.2)
Collecting timm
  Downloading timm-1.0.15-py3-none-any.whl (2.4 MB)
Requirement already satisfied: torch>=1.9.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from open-clip-torch) (2.2.2)
Requirement already satisfied: safetensors in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from open-clip-torch) (0.5.3)
Requirement already satisfied: tqdm in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from open-clip-torch) (4.67.1)
Requirement already satisfied: regex in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from open-clip-torch) (2024.11.6)
Requirement already satisfied: huggingface-hub in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from open-clip-torch) (0.30.2)
Requirement already satisfied: typing-extensions>=4.8.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torch>=1.9.0->open-clip-torch) (4.13.2)
Requirement already satisfied: jinja2 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torch>=1.9.0->open-clip-torch) (3.1.6)
Requirement already satisfied: sympy in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torch>=1.9.0->open-clip-torch) (1.14.0)
Requirement already satisfied: filelock in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torch>=1.9.0->open-clip-torch) (3.18.0)
Requirement already satisfied: fsspec in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torch>=1.9.0->open-clip-torch) (2024.12.0)
Requirement already satisfied: networkx in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torch>=1.9.0->open-clip-torch) (3.2.1)
Collecting wcwidth
  Downloading wcwidth-0.2.13-py2.py3-none-any.whl (34 kB)
Requirement already satisfied: requests in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from huggingface-hub->open-clip-torch) (2.32.3)
Requirement already satisfied: packaging>=20.9 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from huggingface-hub->open-clip-torch) (25.0)
Requirement already satisfied: pyyaml>=5.1 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from huggingface-hub->open-clip-torch) (6.0.2)
Requirement already satisfied: MarkupSafe>=2.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from jinja2->torch>=1.9.0->open-clip-torch) (3.0.2)
Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->huggingface-hub->open-clip-torch) (2025.1.31)
Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->huggingface-hub->open-clip-torch) (3.10)
Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->huggingface-hub->open-clip-torch) (3.4.1)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->huggingface-hub->open-clip-torch) (2.4.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from sympy->torch>=1.9.0->open-clip-torch) (1.3.0)
Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torchvision->open-clip-torch) (11.2.1)
Requirement already satisfied: numpy in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torchvision->open-clip-torch) (2.0.2)
Installing collected packages: wcwidth, timm, ftfy, open-clip-torch
Successfully installed ftfy-6.3.1 open-clip-torch-2.32.0 timm-1.0.15 wcwidth-0.2.13
