

## 🛠 Technology Stack

### Backend
- **FastAPI** - High-performance Python web framework
- **SQLAlchemy** - Database ORM with SQLite
- **Google Gemini 2.5** - Advanced multimodal AI for video understanding
- **ChromaDB** - Vector database for semantic search
- **FFmpeg** - Video processing and frame extraction
- **yt-dlp** - YouTube video downloading

### Frontend
- **React + TypeScript** - Modern web interface
- **Tailwind CSS** - Utility-first styling
- **Vite** - Fast development and build tool
- **Lucide React** - Beautiful icons

## 📋 Prerequisites

- Python 3.8+
- Node.js 16+
- FFmpeg installed
- Google Gemini API key

## 🔧 Installation

### 1. Clone the Repository
```bash
git clone https://github.com/alhridoy/vidchat.git
cd vidchat
```

### 2. Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 3. Environment Configuration
Create `.env` file in the backend directory:
```env
GEMINI_API_KEY=your_gemini_api_key_here
```

### 4. Frontend Setup
```bash
cd ../  # Back to root directory
npm install
```

## 🚀 Running the Application

### Start Backend Server
```bash
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Start Frontend Development Server
```bash
npm run dev
```


##Architecture Design

Component Architecture
![image](https://github.com/user-attachments/assets/b90645f2-d3af-4faf-ba68-22a7ace41015)

Video Processing and Analysis Pipeline
![image](https://github.com/user-attachments/assets/64c06d99-fc30-430f-ac4f-bd69ad216b00)

## Feature Guide
Challenge Requirements vs Your Implementation
1. Video Chat with RAG ✅
Challenge: Chat interface that answers questions about video content using RAG.

Implementation: The system uses vector search to find relevant transcript segments and generates contextual responses using Gemini AI.

2. Timestamped Citations ✅
Challenge: Responses should include clickable timestamps that jump to specific video sections.

 Implementation: GeminiService.generate_chat_response() includes citation extraction functionality that creates navigable links between AI responses and video timestamps.

3. Visual Content Search ✅
Challenge: Natural language queries to find specific visual content in frames (e.g., "red car").

Implementation:multiple approaches:

Direct Visual Search:  implements native video search using Gemini 2.5's video understanding capabilities
Multimodal Embeddings:  CLIP for direct image embeddings combined with text embeddings
Hybrid Search: combines keyword matching with vector similarit
##  Usage Guide

### 1. **Upload or Add YouTube Video**
- Paste a YouTube URL to analyze online content
- Wait for processing to complete (includes transcript extraction and frame analysis)

### 2. **Chat with Video**
- Use the chat interface to ask questions about the video
- Get responses with clickable timestamps
- Citations show relevant video moments

### 3. **Visual Search**
- Click "Visual Search" tab
- Enter natural language queries (e.g., "person with microphone", "red object")
- Browse results with confidence scores
- Jump to specific video moments

### 4. **Browse Sections**
- View auto-generated video sections
- Click timestamps to navigate to specific moments
- Explore key topics for each section






