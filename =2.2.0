Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: sentence-transformers in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (4.1.0)
Requirement already satisfied: huggingface-hub>=0.20.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from sentence-transformers) (0.30.2)
Requirement already satisfied: typing_extensions>=4.5.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from sentence-transformers) (4.13.2)
Requirement already satisfied: Pillow in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from sentence-transformers) (11.2.1)
Requirement already satisfied: torch>=1.11.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from sentence-transformers) (2.2.2)
Requirement already satisfied: scikit-learn in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from sentence-transformers) (1.6.1)
Requirement already satisfied: transformers<5.0.0,>=4.41.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from sentence-transformers) (4.52.3)
Requirement already satisfied: tqdm in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from sentence-transformers) (4.67.1)
Requirement already satisfied: scipy in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from sentence-transformers) (1.13.1)
Requirement already satisfied: packaging>=20.9 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (25.0)
Requirement already satisfied: pyyaml>=5.1 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (6.0.2)
Requirement already satisfied: filelock in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (3.18.0)
Requirement already satisfied: fsspec>=2023.5.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2024.12.0)
Requirement already satisfied: requests in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2.32.3)
Requirement already satisfied: networkx in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torch>=1.11.0->sentence-transformers) (3.2.1)
Requirement already satisfied: sympy in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torch>=1.11.0->sentence-transformers) (1.14.0)
Requirement already satisfied: jinja2 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from torch>=1.11.0->sentence-transformers) (3.1.6)
Requirement already satisfied: numpy>=1.17 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2.0.2)
Requirement already satisfied: safetensors>=0.4.3 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (0.5.3)
Requirement already satisfied: tokenizers<0.22,>=0.21 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (0.21.1)
Requirement already satisfied: regex!=2019.12.17 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2024.11.6)
Requirement already satisfied: MarkupSafe>=2.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from jinja2->torch>=1.11.0->sentence-transformers) (3.0.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (2.4.0)
Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (3.10)
Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (2025.1.31)
Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (3.4.1)
Requirement already satisfied: joblib>=1.2.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from scikit-learn->sentence-transformers) (1.5.0)
Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from scikit-learn->sentence-transformers) (3.6.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /Users/<USER>/Library/Python/3.9/lib/python/site-packages (from sympy->torch>=1.11.0->sentence-transformers) (1.3.0)
